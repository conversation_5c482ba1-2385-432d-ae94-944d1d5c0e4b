import os
import uuid
import time
from flask import Blueprint, request, jsonify, render_template, current_app, send_from_directory

from .services.map_processor import UnifiedMapProcessor

bp = Blueprint('main', __name__)

# 全局存储分析结果
analysis_cache = {}


def cleanup_expired_cache():
    """清理超过1小时的缓存数据"""
    current_time = time.time()
    expired_keys = [
        key for key, value in analysis_cache.items()
        if current_time - value.get('timestamp', 0) > 3600  # 1小时
    ]
    for key in expired_keys:
        del analysis_cache[key]
    if expired_keys:
        print(f"清理了 {len(expired_keys)} 个过期的分析缓存")


def allowed_file(filename: str) -> bool:
    allowed = current_app.config['ALLOWED_EXTENSIONS']
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed


@bp.get('/')
def index():
    return render_template('index.html')


@bp.get('/visualize')
def visualize():
    return render_template('visualize.html')


@bp.get('/visualize/<analysis_id>')
def visualize_with_id(analysis_id):
    return render_template('visualize.html', analysis_id=analysis_id)


@bp.get('/api/analysis/<analysis_id>')
def get_analysis_data(analysis_id):
    """获取分析数据的API端点"""
    if analysis_id not in analysis_cache:
        return jsonify({"error": "分析结果不存在或已过期"}), 404

    return jsonify(analysis_cache[analysis_id]['data'])


@bp.get('/api/analysis/<analysis_id>/nodes')
def get_analysis_nodes(analysis_id):
    """获取节点数据的API端点（分块）"""
    if analysis_id not in analysis_cache:
        return jsonify({"error": "分析结果不存在或已过期"}), 404

    data = analysis_cache[analysis_id]['data']
    chunk_size = int(request.args.get('chunk_size', 1000))
    offset = int(request.args.get('offset', 0))

    # 获取所有节点数据
    analysis_data = data.get('analysis', {})
    all_nodes = {
        'base_map_nodes': data.get('base_map_nodes', []),
        'new_map_nodes': data.get('new_map_nodes', []),
        'added_nodes': analysis_data.get('added_nodes', []),
        'deleted_nodes': analysis_data.get('deleted_nodes', []),
        'conflicts_id_only': analysis_data.get('conflicts_id_only', []),
        'conflicts_id_and_attr': analysis_data.get('conflicts_id_and_attr', [])
    }

    # 分块返回
    result = {}
    for key, nodes in all_nodes.items():
        end_idx = min(offset + chunk_size, len(nodes))
        result[key] = nodes[offset:end_idx]
        result[f'{key}_total'] = len(nodes)
        result[f'{key}_has_more'] = end_idx < len(nodes)

    return jsonify(result)


@bp.get('/api/analysis/<analysis_id>/edges')
def get_analysis_edges(analysis_id):
    """获取边数据的API端点（分块）"""
    if analysis_id not in analysis_cache:
        return jsonify({"error": "分析结果不存在或已过期"}), 404

    data = analysis_cache[analysis_id]['data']
    chunk_size = int(request.args.get('chunk_size', 1000))
    offset = int(request.args.get('offset', 0))

    # 获取所有边数据
    all_edges = {
        'base_map_edges': data.get('base_map_edges', []),
        'new_map_edges': data.get('new_map_edges', []),
        'added_edges': data.get('analysis', {}).get('added_edges', []),
        'deleted_edges': data.get('analysis', {}).get('deleted_edges', [])
    }

    # 分块返回
    result = {}
    for key, edges in all_edges.items():
        end_idx = min(offset + chunk_size, len(edges))
        result[key] = edges[offset:end_idx]
        result[f'{key}_total'] = len(edges)
        result[f'{key}_has_more'] = end_idx < len(edges)

    return jsonify(result)


@bp.get('/api/analysis/<analysis_id>/features')
def get_analysis_features(analysis_id):
    """获取特征数据的API端点（孤岛、跳点边）"""
    if analysis_id not in analysis_cache:
        return jsonify({"error": "分析结果不存在或已过期"}), 404

    data = analysis_cache[analysis_id]['data']
    analysis = data.get('analysis', {})

    return jsonify({
        'old_map_jumping_edges': analysis.get('old_map_jumping_edges', []),
        'new_map_jumping_edges': analysis.get('new_map_jumping_edges', []),
        'old_map_islands': analysis.get('old_map_islands', []),
        'new_map_islands': analysis.get('new_map_islands', [])
    })


@bp.get('/api/analysis/<analysis_id>/excel')
def get_excel_filename(analysis_id):
    """获取Excel文件名的API端点"""
    if analysis_id not in analysis_cache:
        print(f"分析ID {analysis_id} 不存在于缓存中")
        print(f"当前缓存的ID: {list(analysis_cache.keys())}")
        return jsonify({"error": "分析结果不存在或已过期"}), 404

    excel_filename = analysis_cache[analysis_id].get('excel_filename')
    print(f"获取Excel文件名: {excel_filename}")

    if not excel_filename:
        return jsonify({"error": "Excel文件名为空"}), 404

    # 检查文件是否实际存在
    file_path = os.path.join(current_app.config['DOWNLOAD_FOLDER'], excel_filename)
    if not os.path.exists(file_path):
        print(f"Excel文件不存在: {file_path}")
        return jsonify({"error": "Excel文件不存在"}), 404

    return jsonify({
        'excel_filename': excel_filename
    })


@bp.get('/api/analysis/<analysis_id>/info')
def get_analysis_info(analysis_id):
    """获取分析基本信息的API端点"""
    if analysis_id not in analysis_cache:
        return jsonify({"error": "分析结果不存在或已过期"}), 404

    cache_data = analysis_cache[analysis_id]
    data = cache_data['data']

    return jsonify({
        'old_map_filename': cache_data.get('old_map_filename', '旧地图'),
        'new_map_filename': cache_data.get('new_map_filename', '新地图'),
        'statistics': {
            'old_map': {
                'nodes_count': len(data.get('base_map_nodes', [])),
                'edges_count': len(data.get('base_map_edges', [])),
                'islands_count': len(data.get('analysis', {}).get('old_map_islands', [])),
                'jumping_edges_count': len(data.get('analysis', {}).get('old_map_jumping_edges', []))
            },
            'new_map': {
                'nodes_count': len(data.get('new_map_nodes', [])),
                'edges_count': len(data.get('new_map_edges', [])),
                'islands_count': len(data.get('analysis', {}).get('new_map_islands', [])),
                'jumping_edges_count': len(data.get('analysis', {}).get('new_map_jumping_edges', []))
            },
            'differences': {
                'added_nodes_count': len(data.get('analysis', {}).get('added_nodes', [])),
                'deleted_nodes_count': len(data.get('analysis', {}).get('deleted_nodes', [])),
                'modified_nodes_count': len(data.get('analysis', {}).get('conflicts_id_only', [])) + len(data.get('analysis', {}).get('conflicts_id_and_attr', [])),
                'added_edges_count': len(data.get('analysis', {}).get('added_edges', [])),
                'deleted_edges_count': len(data.get('analysis', {}).get('deleted_edges', []))
            }
        }
    })


@bp.post('/analyze')
def analyze_maps():
    if 'oldMap' not in request.files or 'newMap' not in request.files:
        return jsonify({"error": "缺少文件"}), 400

    old_map_file = request.files['oldMap']
    new_map_file = request.files['newMap']

    if old_map_file.filename == '' or new_map_file.filename == '':
        return jsonify({"error": "未选择文件"}), 400

    if old_map_file and allowed_file(old_map_file.filename) and new_map_file and allowed_file(new_map_file.filename):
        old_filename = f"old_{uuid.uuid4().hex}.xml"
        new_filename = f"new_{uuid.uuid4().hex}.xml"
        old_filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], old_filename)
        new_filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], new_filename)
        old_map_file.save(old_filepath)
        new_map_file.save(new_filepath)
        try:
            processor = UnifiedMapProcessor(old_filepath, new_filepath, download_folder=current_app.config['DOWNLOAD_FOLDER'])
            frontend_data, excel_filename = processor.run_all_analyses()

            # 生成分析ID并存储结果
            analysis_id = uuid.uuid4().hex
            analysis_cache[analysis_id] = {
                'data': frontend_data,
                'excel_filename': excel_filename,
                'timestamp': time.time(),
                'old_map_filename': os.path.splitext(os.path.basename(old_filepath))[0],
                'new_map_filename': os.path.splitext(os.path.basename(new_filepath))[0]
            }

            # 清理过期数据（超过1小时）
            cleanup_expired_cache()

            return jsonify({
                'analysis_id': analysis_id,
                'excel_filename': excel_filename,
                'summary': {
                    'base_nodes_count': len(frontend_data.get('base_map_nodes', [])),
                    'new_nodes_count': len(frontend_data.get('new_map_nodes', [])),
                    'added_nodes_count': len(frontend_data.get('analysis', {}).get('added_nodes', [])),
                    'deleted_nodes_count': len(frontend_data.get('analysis', {}).get('deleted_nodes', [])),
                    'modified_nodes_count': len(frontend_data.get('analysis', {}).get('conflicts_id_only', [])) + len(frontend_data.get('analysis', {}).get('conflicts_id_and_attr', []))
                }
            })
        except Exception as e:
            import traceback
            print(f"分析过程中发生严重错误: {e}")
            traceback.print_exc()
            return jsonify({"error": f"后端处理失败: {e}"}), 500
        finally:
            # 清理上传文件
            try:
                os.remove(old_filepath)
            except Exception:
                pass
            try:
                os.remove(new_filepath)
            except Exception:
                pass
    else:
        return jsonify({"error": "文件类型不被允许"}), 400


@bp.route('/download-excel/<filename>')
def download_file(filename):
    print(f"=== 下载路由被调用 ===")
    print(f"请求下载文件: {filename}")

    download_folder = current_app.config['DOWNLOAD_FOLDER']
    print(f"配置的下载目录: {download_folder}")

    # 获取绝对路径
    abs_download_folder = os.path.abspath(download_folder)
    abs_file_path = os.path.abspath(os.path.join(download_folder, filename))

    print(f"绝对下载目录: {abs_download_folder}")
    print(f"绝对文件路径: {abs_file_path}")
    print(f"下载目录是否存在: {os.path.exists(abs_download_folder)}")
    print(f"文件是否存在: {os.path.exists(abs_file_path)}")

    # 列出下载目录中的所有文件
    if os.path.exists(abs_download_folder):
        files_in_dir = os.listdir(abs_download_folder)
        print(f"下载目录中的文件: {files_in_dir}")
    else:
        print("下载目录不存在！")
        return jsonify({"error": "下载目录不存在"}), 500

    if not os.path.exists(abs_file_path):
        print(f"文件不存在: {abs_file_path}")
        return jsonify({"error": f"文件不存在: {filename}"}), 404

    try:
        print(f"开始发送文件: {filename}")
        print(f"使用send_from_directory: directory={abs_download_folder}, filename={filename}")

        # 尝试使用Flask的send_from_directory
        from flask import send_file
        print("尝试方法1: send_from_directory")
        try:
            return send_from_directory(abs_download_folder, filename, as_attachment=True)
        except Exception as e1:
            print(f"send_from_directory失败: {e1}")

            # 备用方法：直接使用send_file
            print("尝试方法2: send_file")
            return send_file(abs_file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        print(f"所有发送方法都失败: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"下载失败: {str(e)}"}), 500


@bp.route('/test-download')
def test_download():
    """测试路由是否工作"""
    return jsonify({"message": "下载路由工作正常", "timestamp": time.time()})







