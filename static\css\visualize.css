* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f0f0f0;
    overflow: hidden;
}

.header {
    background: #2c3e50;
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 1000;
    position: relative;
}

.header h1 {
    font-size: 20px;
}

.controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn:hover {
    background: #2980b9;
}

.btn.active {
    background: #e74c3c;
}

.btn.active:hover {
    background: #c0392b;
}

.main-container {
    display: flex;
    height: calc(100vh - 60px);
}

.sidebar {
    width: 300px;
    background: white;
    border-right: 1px solid #ddd;
    overflow-y: auto;
    padding: 20px;
}

.map-container {
    flex: 1;
    position: relative;
    background: #fff;
}

#mapCanvas {
    display: block;
    cursor: grab;
}

#mapCanvas:active {
    cursor: grabbing;
}

.legend {
    margin-bottom: 25px;
}

.legend h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
    font-size: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    border: 1px solid transparent;
}

.legend-item:hover {
    background-color: #f8f9fa;
    border-color: #e9ecef;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    margin-right: 12px;
    border: 1px solid #ccc;
    flex-shrink: 0;
}

.legend-label {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.legend-toggle {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #3498db;
}

.zoom-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 100;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.2s;
}

.zoom-btn:hover {
    background: #f0f0f0;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.zoom-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.minimap {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    background: white;
    border: 2px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 100;
    overflow: hidden;
}

#minimapCanvas {
    width: 100%;
    height: 100%;
    cursor: pointer;
    display: block;
}

.viewport-indicator {
    position: absolute;
    border: 2px solid #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    pointer-events: none;
    border-radius: 2px;
}

.stats {
    background: #ecf0f1;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #bdc3c7;
}

.stats h4 {
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 16px;
}

.stat-item {
     display: flex;
  justify-content: space-between;
  align-items: flex-start; /* 推荐：防止文本换行后，短文本与长文本顶部对不齐 */
  gap: 16px; /* 推荐：在标签和值之间设置一个最小间距，防止它们贴得太近 */

  /* 你原有的样式 */
  margin-bottom: 8px;
  font-size: 14px;
}

.stat-item span:first-child {
    color: #555;
     min-width: 0; /* 关键！允许flex子项收缩到比其内容更窄，从而触发换行 */
  overflow-wrap: break-word; /* 标准的文本换行属性，优先在单词间换行 */
  text-align: right; /* 让右侧的文本靠右对齐，视觉上更整齐 */
}

.stat-item span:last-child {
    font-weight: bold;
    color: #2c3e50;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
    font-size: 18px;
}

.error-message {
    background: #e74c3c;
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin: 20px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 250px;
    }
    
    .header h1 {
        font-size: 16px;
    }
    
    .minimap {
        width: 150px;
        height: 112px;
    }
    
    .zoom-controls {
        right: 10px;
        top: 10px;
    }
    
    .zoom-btn {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
    width: 8px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
