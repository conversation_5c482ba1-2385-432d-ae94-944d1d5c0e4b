<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图差异可视化</title>
    <link rel="stylesheet" href="/static/css/visualize.css">
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div>正在加载地图数据...</div>
    </div>
    
    <div class="header">
        <h1>地图差异可视化</h1>
        <div class="controls">
            <button class="btn" id="toggleMapBtn">切换到新地图</button>
            <button class="btn btn-secondary" id="downloadExcelBtn">下载Excel报告</button>
            <button class="btn btn-secondary" id="saveHtmlBtn">保存HTML</button>
            <button class="btn" onclick="window.location.href='/'">返回首页</button>
        </div>
    </div>
    
    <div class="main-container">
        <div class="sidebar">
            <div class="stats" id="mapStats">
                <h4>地图统计</h4>
                <div class="stat-item">
                    <span>当前基准:</span>
                    <span id="currentBase">加载中...</span>
                </div>
                <div class="stat-item">
                    <span>节点总数:</span>
                    <span id="nodeCount">0</span>
                </div>
                <div class="stat-item">
                    <span>边总数:</span>
                    <span id="edgeCount">0</span>
                </div>
                <div class="stat-item">
                    <span>孤岛数量:</span>
                    <span id="islandCount">0</span>
                </div>
                <div class="stat-item">
                    <span>跳点边数:</span>
                    <span id="jumpingEdgeCount">0</span>
                </div>
            </div>

            <div class="stats" id="differenceStats" style="display: none;">
                <h4>差异统计</h4>
                <div class="stat-item">
                    <span>新增节点:</span>
                    <span id="addedNodesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>删除节点:</span>
                    <span id="deletedNodesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>修改节点:</span>
                    <span id="modifiedNodesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>新增边:</span>
                    <span id="addedEdgesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>删除边:</span>
                    <span id="deletedEdgesCount">0</span>
                </div>
            </div>
            
            <div class="legend" id="oldMapLegend">
                <h3>旧地图图例</h3>
                <!-- 图例项将通过JavaScript动态生成 -->
            </div>
            
            <div class="legend" id="newMapLegend" style="display: none;">
                <h3>新地图图例</h3>
                <!-- 图例项将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <div class="map-container">
            <canvas id="mapCanvas"></canvas>
            
            <div class="zoom-controls">
                <button class="zoom-btn" id="zoomInBtn">+</button>
                <button class="zoom-btn" id="zoomOutBtn">−</button>
                <button class="zoom-btn" id="resetViewBtn" title="重置视图">⌂</button>
            </div>
            
            <div class="minimap">
                <canvas id="minimapCanvas"></canvas>
                <div class="viewport-indicator" id="viewportIndicator"></div>
            </div>
        </div>
    </div>
    
    {% if analysis_id %}
    <script>
        // 设置分析ID到全局变量
        window.ANALYSIS_ID = '{{ analysis_id }}';
    </script>
    {% endif %}
    <script src="/static/js/map-visualizer.js"></script>
</body>
</html>
