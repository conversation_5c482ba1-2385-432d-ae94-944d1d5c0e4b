1.修改输出的EXCEL sheet名字， 5.1_Conflicts_ID_Only  5.2_Conflicts_ID_and_Attr 修改为 Changes_ID_only和相应的ID and Attr，同时修改所有和这个excel相关的内容，保证功能没有问题；

2.在原本输出的Excel6.2和7.1中间增加一个总结部分，用来统计总共有多少个内容，原本的7.1修改为8.1 以此类推 ，新增的部分为旧地图和新地图的统计数据具体需要输出的内容如下：


| island_id | node_count | edge_count | source_nodes_in_island | dead_end_nodes_in_island | summary                                 | path_info                                              |  |  |  |  |  |
| --------- | ---------- | ---------- | ---------------------- | ------------------------ | --------------------------------------- | ------------------------------------------------------ | - | - | - | - | - |
| 1         | 272        | 373        | 485000311528           | 485000294717             | 这是一个有入口和/或出口的复杂孤立子图。 | 所有入口: ['485000311528']. 所有出口: ['485000294717'] |  |  |  |  |  |
| 2         | 42         | 41         | 485000295375           | 485000295194             | 这是一个有入口和/或出口的复杂孤立子图。 | 所有入口: ['485000295375']. 所有出口: ['485000295194'] |  |  |  |  |  |
| 3         | 24         | 35         | 无                     | 无                       | 这是一个孤立的环路或闭合子图。          | 示例环路: 485000297026 -> 485000297027 -> 485000297026 |  |  |  |  |  |
| 4         | 42         | 41         | 485000311665           | 485000294832             | 这是一个有入口和/或出口的复杂孤立子图。 | 所有入口: ['485000311665']. 所有出口: ['485000294832'] |  |  |  |  |  |
| 5         | 5          | 4          | 485000296108           | 485000296114             | 这是一个有入口和/或出口的复杂孤立子图。 | 所有入口: ['485000296108']. 所有出口: ['485000296114'] |  |  |  |  |  |
| 6         | 2          | 2          | 无                     | 无                       | 这是一个孤立的环路或闭合子图。          | 示例环路: 485000311235 -> 485000311250 -> 485000311235 |  |  |  |  |  |

请你参考这个表格的形式给我一份相应的统计，注意，要包括把一万多个点多包括的那个总的孤岛

3.在页面入口http://127.0.0.1:5000/visualize/页面下显示一些关键的统计数据，以及一些修改，主要包括：

当前基准请你不要用旧地图 、新地图区分，而是用原本的文件名去掉后缀的显示；页面的图例中，请你在增加统计信息，在基础节点、基础边等图例的后面用括号显示相应的数量

4.增加新功能：保存当前的html，由于我们使用js读取数据然后生成，我希望你能给我一个功能，就是把当前的html带着数据的部分直接保存为一整个html，我可以直接分享给别人那种
