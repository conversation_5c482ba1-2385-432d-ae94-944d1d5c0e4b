import os
import uuid
import xml.etree.ElementTree as ET
from collections import defaultdict, deque
import pandas as pd


class UnifiedMapProcessor:
    """
    统一的地图处理器，整合分析与比较功能。
    与原始实现等价，但将与 Flask 解耦并通过参数注入下载目录。
    """

    def __init__(self, old_map_path: str, new_map_path: str, download_folder: str = 'downloads'):
        print("--- 初始化统一地图处理器 ---")
        self.download_folder = download_folder
        self.old_nodes, self.old_edges = self._parse_xml(old_map_path)
        self.old_adj, self.old_undirected_adj, self.old_in_degree, self.old_out_degree = self._build_graph_helpers(self.old_nodes, self.old_edges)

        self.new_nodes, self.new_edges = self._parse_xml(new_map_path)
        self.new_adj, self.new_undirected_adj, self.new_in_degree, self.new_out_degree = self._build_graph_helpers(self.new_nodes, self.new_edges)

        self.old_nodes_xy = {(node.get('x'), node.get('y')): node for node in self.old_nodes.values() if node.get('x') and node.get('y')}
        self.new_nodes_xy = {(node.get('x'), node.get('y')): node for node in self.new_nodes.values() if node.get('x') and node.get('y')}
        print("新旧地图加载解析完成。")

    def _parse_xml(self, file_path):
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            nodes = {elem.get('id'): {k: v for k, v in elem.attrib.items()} for elem in root.findall('.//node') if elem.get('id')}
            edges = {elem.get('id'): {k: v for k, v in elem.attrib.items()} for elem in root.findall('.//edge') if elem.get('id')}
            return nodes, edges
        except Exception as e:
            print(f"解析XML文件 '{file_path}' 时出错: {e}")
            return {}, {}

    def _build_graph_helpers(self, nodes, edges):
        node_ids = set(nodes.keys())
        adj = defaultdict(list)
        undirected_adj = defaultdict(list)
        in_degree = {node_id: 0 for node_id in node_ids}
        out_degree = {node_id: 0 for node_id in node_ids}

        for edge in edges.values():
            start_node, end_node = edge.get('start_node_id'), edge.get('end_node_id')
            if start_node in node_ids and end_node in node_ids:
                adj[start_node].append(end_node)
                undirected_adj[start_node].append(end_node)
                undirected_adj[end_node].append(start_node)
                out_degree[start_node] += 1
                in_degree[end_node] += 1
        return adj, undirected_adj, in_degree, out_degree

    def compare_maps(self):
        print("开始比较地图差异...")
        old_node_ids = set(self.old_nodes.keys())
        new_node_ids = set(self.new_nodes.keys())
        old_edge_ids = set(self.old_edges.keys())
        new_edge_ids = set(self.new_edges.keys())

        added_node_ids = new_node_ids - old_node_ids
        deleted_node_ids = old_node_ids - new_node_ids
        added_nodes = [self.new_nodes[nid] for nid in added_node_ids]
        deleted_nodes = [self.old_nodes[nid] for nid in deleted_node_ids]

        added_edge_ids = new_edge_ids - old_edge_ids
        deleted_edge_ids = old_edge_ids - new_edge_ids
        added_edges = [self.new_edges[eid] for eid in added_edge_ids]
        deleted_edges = [self.old_edges[eid] for eid in deleted_edge_ids]

        conflicts_id_only, conflicts_id_and_attr = self._find_xy_conflicts()

        print("差异比较完成。")
        return {
            "added_nodes": added_nodes,
            "deleted_nodes": deleted_nodes,
            "added_edges": added_edges,
            "deleted_edges": deleted_edges,
            "conflicts_id_only": conflicts_id_only,
            "conflicts_id_and_attr": conflicts_id_and_attr,
        }

    def _find_xy_conflicts(self):
        conflicts_id_only = []
        conflicts_id_and_attr = []
        common_coords = set(self.old_nodes_xy.keys()).intersection(set(self.new_nodes_xy.keys()))

        for coord in common_coords:
            old_node, new_node = self.old_nodes_xy[coord], self.new_nodes_xy[coord]
            if old_node.get('id') == new_node.get('id'):
                continue

            all_keys = set(old_node.keys()).union(set(new_node.keys()))
            other_attrs_different = any(old_node.get(key) != new_node.get(key) for key in all_keys if key != 'id')

            conflict_data = {'x': coord[0], 'y': coord[1], 'old_node': old_node, 'new_node': new_node}
            if other_attrs_different:
                conflicts_id_and_attr.append(conflict_data)
            else:
                conflicts_id_only.append(conflict_data)
        return conflicts_id_only, conflicts_id_and_attr

    def analyze_isolates(self, nodes, edges, undirected_adj, in_degree, out_degree, adj):
        unvisited_nodes, islands_of_nodes = set(nodes.keys()), []
        while unvisited_nodes:
            start_node, current_island_nodes = unvisited_nodes.pop(), set()
            q_island, visited_island = deque([start_node]), {start_node}
            current_island_nodes.add(start_node)
            while q_island:
                current = q_island.popleft()
                for neighbor in undirected_adj.get(current, []):
                    if neighbor in unvisited_nodes and neighbor not in visited_island:
                        visited_island.add(neighbor)
                        unvisited_nodes.discard(neighbor)
                        current_island_nodes.add(neighbor)
                        q_island.append(neighbor)
            islands_of_nodes.append(current_island_nodes)

        islands_report = []
        for i, island_nodes_set in enumerate(islands_of_nodes):
            island_nodes = list(island_nodes_set)
            island_edges = [e for e in edges.values() if e.get('start_node_id') in island_nodes_set]
            source_nodes = [n for n in island_nodes if in_degree.get(n, 0) == 0]
            dead_end_nodes = [n for n in island_nodes if out_degree.get(n, 0) == 0]
            summary = "有向无环子图或复杂图"
            if len(island_nodes) == 1 and not island_edges:
                summary = "真孤立点 (无边连接)"
            elif not source_nodes and not dead_end_nodes and island_edges:
                summary = "孤立环路或闭合图"
            islands_report.append({
                "island_id": i + 1,
                "node_count": len(island_nodes),
                "edge_count": len(island_edges),
                "nodes": [nodes[nid] for nid in island_nodes],
                "edges": island_edges,
                "summary": summary,
            })
        print(f"找到 {len(islands_report)} 个孤立子图（孤岛）。")
        return islands_report

    def generate_islands_summary(self, old_map_islands, new_map_islands):
        """生成孤岛统计总结"""
        summary_data = []

        # 处理旧地图孤岛
        for island in old_map_islands:
            source_nodes = []
            dead_end_nodes = []

            # 分析孤岛中的源节点和终点节点
            island_node_ids = {node['id'] for node in island['nodes']}

            for node in island['nodes']:
                node_id = node['id']
                # 检查是否为源节点（入度为0）
                if self.old_in_degree.get(node_id, 0) == 0:
                    source_nodes.append(node_id)
                # 检查是否为终点节点（出度为0）
                if self.old_out_degree.get(node_id, 0) == 0:
                    dead_end_nodes.append(node_id)

            # 生成路径信息
            path_info = self.generate_path_info(island, source_nodes, dead_end_nodes)

            summary_data.append({
                'map_type': '旧地图',
                'island_id': island['island_id'],
                'node_count': island['node_count'],
                'edge_count': island['edge_count'],
                'source_nodes_in_island': ', '.join(source_nodes) if source_nodes else '无',
                'dead_end_nodes_in_island': ', '.join(dead_end_nodes) if dead_end_nodes else '无',
                'summary': self.get_detailed_summary(island, source_nodes, dead_end_nodes),
                'path_info': path_info
            })

        # 处理新地图孤岛
        for island in new_map_islands:
            source_nodes = []
            dead_end_nodes = []

            # 分析孤岛中的源节点和终点节点
            island_node_ids = {node['id'] for node in island['nodes']}

            for node in island['nodes']:
                node_id = node['id']
                # 检查是否为源节点（入度为0）
                if self.new_in_degree.get(node_id, 0) == 0:
                    source_nodes.append(node_id)
                # 检查是否为终点节点（出度为0）
                if self.new_out_degree.get(node_id, 0) == 0:
                    dead_end_nodes.append(node_id)

            # 生成路径信息
            path_info = self.generate_path_info(island, source_nodes, dead_end_nodes)

            summary_data.append({
                'map_type': '新地图',
                'island_id': island['island_id'],
                'node_count': island['node_count'],
                'edge_count': island['edge_count'],
                'source_nodes_in_island': ', '.join(source_nodes) if source_nodes else '无',
                'dead_end_nodes_in_island': ', '.join(dead_end_nodes) if dead_end_nodes else '无',
                'summary': self.get_detailed_summary(island, source_nodes, dead_end_nodes),
                'path_info': path_info
            })

        return summary_data

    def get_detailed_summary(self, island, source_nodes, dead_end_nodes):
        """生成详细的孤岛描述"""
        if island['node_count'] == 1 and island['edge_count'] == 0:
            return "这是一个真正的孤立点（无边连接）。"
        elif not source_nodes and not dead_end_nodes and island['edge_count'] > 0:
            return "这是一个孤立的环路或闭合子图。"
        elif source_nodes or dead_end_nodes:
            return "这是一个有入口和/或出口的复杂孤立子图。"
        else:
            return "这是一个复杂的孤立子图。"

    def generate_path_info(self, island, source_nodes, dead_end_nodes):
        """生成路径信息"""
        if island['node_count'] == 1 and island['edge_count'] == 0:
            return "孤立点，无路径。"
        elif not source_nodes and not dead_end_nodes and island['edge_count'] > 0:
            # 找一个示例环路
            if island['edges'] and len(island['edges']) > 0:
                first_edge = island['edges'][0]
                start_id = first_edge.get('start_node_id', '')
                end_id = first_edge.get('end_node_id', '')
                return f"示例环路: {start_id} -> {end_id} -> {start_id}"
            return "环路结构，无明确起终点。"
        else:
            source_info = f"所有入口: {source_nodes}" if source_nodes else "无入口"
            dead_end_info = f"所有出口: {dead_end_nodes}" if dead_end_nodes else "无出口"
            return f"{source_info}. {dead_end_info}"

    def find_jumping_edges(self, nodes, edges):
        nodes_by_x, nodes_by_y, jumping_edges_report = defaultdict(list), defaultdict(list), []
        for node in nodes.values():
            if node.get('x') and node.get('y'):
                nodes_by_x[node['x']].append(node)
                nodes_by_y[node['y']].append(node)
        for edge in edges.values():
            start_node_id, end_node_id = edge.get('start_node_id'), edge.get('end_node_id')
            if start_node_id not in nodes or end_node_id not in nodes:
                continue
            start_node, end_node = nodes[start_node_id], nodes[end_node_id]
            x1, y1, x2, y2 = start_node.get('x'), start_node.get('y'), end_node.get('x'), end_node.get('y')
            if not all([x1, y1, x2, y2]):
                continue
            intermediate_nodes = []
            if y1 == y2 and x1 != x2:
                min_x, max_x = min(int(x1), int(x2)), max(int(x1), int(x2))
                intermediate_nodes = [n for n in nodes_by_y.get(y1, []) if n['id'] not in (start_node_id, end_node_id) and min_x < int(n['x']) < max_x]
            elif x1 == x2 and y1 != y2:
                min_y, max_y = min(int(y1), int(y2)), max(int(y1), int(y2))
                intermediate_nodes = [n for n in nodes_by_x.get(x1, []) if n['id'] not in (start_node_id, end_node_id) and min_y < int(n['y']) < max_y]
            if intermediate_nodes:
                jumping_edges_report.append({"jumping_edge": edge, "intermediate_nodes": intermediate_nodes})
        print(f"找到 {len(jumping_edges_report)} 条“跳点边”。")
        return jumping_edges_report

    def run_all_analyses(self):
        print("\n--- 开始执行所有分析任务 ---")
        comparison_results = self.compare_maps()
        print("\n分析旧地图特征...")
        old_map_jumping_edges = self.find_jumping_edges(self.old_nodes, self.old_edges)
        old_map_islands = self.analyze_isolates(self.old_nodes, self.old_edges, self.old_undirected_adj, self.old_in_degree, self.old_out_degree, self.old_adj)
        print("\n分析新地图特征...")
        new_map_jumping_edges = self.find_jumping_edges(self.new_nodes, self.new_edges)
        new_map_islands = self.analyze_isolates(self.new_nodes, self.new_edges, self.new_undirected_adj, self.new_in_degree, self.new_out_degree, self.new_adj)
        frontend_data = {
            "base_map_nodes": list(self.old_nodes.values()),
            "base_map_edges": list(self.old_edges.values()),
            "new_map_nodes": list(self.new_nodes.values()),
            "new_map_edges": list(self.new_edges.values()),
            "analysis": {
                **comparison_results,
                "old_map_jumping_edges": old_map_jumping_edges,
                "old_map_islands": old_map_islands,
                "new_map_jumping_edges": new_map_jumping_edges,
                "new_map_islands": new_map_islands,
            },
        }
        excel_filename = self.export_to_excel(frontend_data['analysis'])
        print("--- 所有分析任务完成 ---")
        return frontend_data, excel_filename

    def export_to_excel(self, analysis_data):
        filename = f"map_analysis_report_{uuid.uuid4().hex[:8]}.xlsx"
        # 确保使用绝对路径
        if not os.path.isabs(self.download_folder):
            download_folder = os.path.abspath(self.download_folder)
        else:
            download_folder = self.download_folder

        # 确保目录存在
        os.makedirs(download_folder, exist_ok=True)

        filepath = os.path.join(download_folder, filename)
        print(f"正在生成Excel报告: {filepath}")
        print(f"下载目录: {download_folder}")
        print(f"目录是否存在: {os.path.exists(download_folder)}")
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            pd.DataFrame(analysis_data['added_nodes']).to_excel(writer, sheet_name='1_Added_Nodes', index=False)
            pd.DataFrame(analysis_data['deleted_nodes']).to_excel(writer, sheet_name='2_Deleted_Nodes', index=False)
            pd.DataFrame(analysis_data['added_edges']).to_excel(writer, sheet_name='3_Added_Edges', index=False)
            pd.DataFrame(analysis_data['deleted_edges']).to_excel(writer, sheet_name='4_Deleted_Edges', index=False)
            pd.json_normalize(analysis_data['conflicts_id_only'], sep='_').to_excel(writer, sheet_name='5.1_Changes_ID_Only', index=False)
            pd.json_normalize(analysis_data['conflicts_id_and_attr'], sep='_').to_excel(writer, sheet_name='5.2_Changes_ID_and_Attr', index=False)
            pd.json_normalize(analysis_data['new_map_jumping_edges'], sep='_').to_excel(writer, sheet_name='6.1_Jumping_Edges_NewMap', index=False)
            pd.json_normalize(analysis_data['old_map_jumping_edges'], sep='_').to_excel(writer, sheet_name='6.2_Jumping_Edges_OldMap', index=False)

            # 生成孤岛统计总结
            islands_summary = self.generate_islands_summary(analysis_data['old_map_islands'], analysis_data['new_map_islands'])
            pd.DataFrame(islands_summary).to_excel(writer, sheet_name='7_Islands_Summary', index=False)

            pd.json_normalize(analysis_data['new_map_islands'], record_path=['nodes'], meta=['island_id', 'summary', 'node_count', 'edge_count'], record_prefix='node_').to_excel(writer, sheet_name='8.1_Islands_NewMap', index=False)
            pd.json_normalize(analysis_data['old_map_islands'], record_path=['nodes'], meta=['island_id', 'summary', 'node_count', 'edge_count'], record_prefix='node_').to_excel(writer, sheet_name='8.2_Islands_OldMap', index=False)
        print("Excel报告生成完毕。")
        print(f"文件是否存在: {os.path.exists(filepath)}")
        if os.path.exists(filepath):
            print(f"文件大小: {os.path.getsize(filepath)} bytes")
        return filename

