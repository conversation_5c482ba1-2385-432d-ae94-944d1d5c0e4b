class MapVisualizer {
    constructor() {
        // 地图坐标范围
        this.MAP_BOUNDS = {
            minX: 113800,
            maxX: 687950,
            minY: 97600,
            maxY: 497950
        };
        
        // 计算地图尺寸
        this.MAP_WIDTH = this.MAP_BOUNDS.maxX - this.MAP_BOUNDS.minX;
        this.MAP_HEIGHT = this.MAP_BOUNDS.maxY - this.MAP_BOUNDS.minY;
        
        // 画布和上下文
        this.canvas = document.getElementById('mapCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.minimapCanvas = document.getElementById('minimapCanvas');
        this.minimapCtx = this.minimapCanvas.getContext('2d');
        
        // 视图状态
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.isDragging = false;
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        
        // 当前显示模式
        this.currentMode = 'old'; // 'old' 或 'new'
        
        // 图层可见性
        this.layerVisibility = {
            old: {
                islands: true,
                jumpingEdges: true,
                addedNodes: true,
                modifiedNodes: true,
                deletedNodes: true,
                baseNodes: true,
                baseEdges: true
            },
            new: {
                islands: true,
                jumpingEdges: true,
                baseNodes: true,
                baseEdges: true
            }
        };
        
        // 颜色配置
        this.colors = {
            baseNode: '#4a90e2',
            baseEdge: '#7ed321',
            addedNode: '#50e3c2',
            modifiedNode: '#f5a623',
            deletedNode: '#d0021b',
            island: '#9013fe',
            jumpingEdge: '#bd10e0',
            highlight: '#FFFF00' // 新增高亮颜色
        };
        
        // 数据
        this.data = null;
        this.analysisInfo = null;
        
        // 优化：节点查找地图
        this.oldNodeMap = new Map();
        this.newNodeMap = new Map();
        
        // 渲染请求标志
        this.renderRequested = false;
        
        // 元素详情气泡
        this.popup = null;
        this.popupCloseListener = null;

        // *** 新增：当前选中的元素 ***
        this.selectedElement = null;

        this.init();
    }
    
    async init() {
        try {
            // 获取分析ID
            const analysisId = this.getAnalysisId();

            if (analysisId) {
                // 使用分块加载数据
                await this.loadDataInChunks(analysisId);
                // 加载分析信息
                await this.loadAnalysisInfo(analysisId);
            } else {
                // 尝试从sessionStorage获取数据（向后兼容）
                const dataStr = sessionStorage.getItem('mapAnalysisData');
                if (!dataStr) {
                    throw new Error('未找到地图分析数据');
                }
                this.data = JSON.parse(dataStr);
            }

            console.log('数据加载完成');
            
            // 预处理节点坐标和构建节点地图
            this.preprocessNodes();

            this.setupCanvas();
            this.setupEventListeners();
            this.setupLegend();
            this.resetView();
            this.updateStats();

            // 隐藏加载覆盖层
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        } catch (error) {
            console.error('初始化错误:', error);
            this.showError('加载数据失败: ' + error.message);
        }
    }
    
    preprocessNodes() {
        // 预解析坐标
        const nodeArrays = [
            this.data.base_map_nodes,
            this.data.new_map_nodes,
            this.data.analysis.added_nodes,
            this.data.analysis.deleted_nodes
        ];
        
        // 处理 conflicts 中的节点
        [...this.data.analysis.conflicts_id_only, ...this.data.analysis.conflicts_id_and_attr].forEach(conflict => {
            if (conflict.new_node) {
                conflict.new_node.x = parseFloat(conflict.new_node.x);
                conflict.new_node.y = parseFloat(conflict.new_node.y);
            }
            if (conflict.old_node) {
                conflict.old_node.x = parseFloat(conflict.old_node.x);
                conflict.old_node.y = parseFloat(conflict.old_node.y);
            }
        });
        
        nodeArrays.forEach(arr => {
            arr.forEach(node => {
                if (node) {
                    node.x = parseFloat(node.x);
                    node.y = parseFloat(node.y);
                }
            });
        });
        
        // 构建节点地图
        this.data.base_map_nodes.forEach(node => this.oldNodeMap.set(node.id, node));
        this.data.analysis.added_nodes.forEach(node => this.oldNodeMap.set(node.id, node));
        this.data.analysis.deleted_nodes.forEach(node => this.oldNodeMap.set(node.id, node));
        [...this.data.analysis.conflicts_id_only, ...this.data.analysis.conflicts_id_and_attr].forEach(conflict => {
            if (conflict.new_node) this.oldNodeMap.set(conflict.new_node.id, conflict.new_node);
            if (conflict.old_node) this.oldNodeMap.set(conflict.old_node.id, conflict.old_node);
        });
        
        this.data.new_map_nodes.forEach(node => this.newNodeMap.set(node.id, node));
    }

    async loadDataInChunks(analysisId) {
        // 初始化数据结构
        this.data = {
            base_map_nodes: [],
            new_map_nodes: [],
            base_map_edges: [],
            new_map_edges: [],
            analysis: {
                added_nodes: [],
                deleted_nodes: [],
                conflicts_id_only: [],
                conflicts_id_and_attr: [],
                added_edges: [],
                deleted_edges: [],
                old_map_jumping_edges: [],
                new_map_jumping_edges: [],
                old_map_islands: [],
                new_map_islands: []
            }
        };

        // 加载节点数据
        await this.loadNodesData(analysisId);

        // 加载边数据
        await this.loadEdgesData(analysisId);

        // 加载特征数据
        await this.loadFeaturesData(analysisId);
    }

    async loadNodesData(analysisId) {
        let offset = 0;
        const chunkSize = 1000;

        while (true) {
            const response = await fetch(`/api/analysis/${analysisId}/nodes?chunk_size=${chunkSize}&offset=${offset}`);
            if (!response.ok) throw new Error('无法加载节点数据');

            const chunk = await response.json();

            // 合并数据
            this.data.base_map_nodes.push(...chunk.base_map_nodes);
            this.data.new_map_nodes.push(...chunk.new_map_nodes);
            this.data.analysis.added_nodes.push(...chunk.added_nodes);
            this.data.analysis.deleted_nodes.push(...chunk.deleted_nodes);
            this.data.analysis.conflicts_id_only.push(...chunk.conflicts_id_only);
            this.data.analysis.conflicts_id_and_attr.push(...chunk.conflicts_id_and_attr);

            // 检查是否还有更多数据
            const hasMore = chunk.base_map_nodes_has_more || chunk.new_map_nodes_has_more ||
                              chunk.added_nodes_has_more || chunk.deleted_nodes_has_more ||
                              chunk.conflicts_id_only_has_more || chunk.conflicts_id_and_attr_has_more;

            if (!hasMore) break;
            offset += chunkSize;
        }
    }

    async loadEdgesData(analysisId) {
        let offset = 0;
        const chunkSize = 1000;

        while (true) {
            const response = await fetch(`/api/analysis/${analysisId}/edges?chunk_size=${chunkSize}&offset=${offset}`);
            if (!response.ok) throw new Error('无法加载边数据');

            const chunk = await response.json();

            // 合并数据
            this.data.base_map_edges.push(...chunk.base_map_edges);
            this.data.new_map_edges.push(...chunk.new_map_edges);
            this.data.analysis.added_edges.push(...chunk.added_edges);
            this.data.analysis.deleted_edges.push(...chunk.deleted_edges);

            // 检查是否还有更多数据
            const hasMore = chunk.base_map_edges_has_more || chunk.new_map_edges_has_more ||
                              chunk.added_edges_has_more || chunk.deleted_edges_has_more;

            if (!hasMore) break;
            offset += chunkSize;
        }
    }

    async loadFeaturesData(analysisId) {
        const response = await fetch(`/api/analysis/${analysisId}/features`);
        if (!response.ok) throw new Error('无法加载特征数据');

        const features = await response.json();
        Object.assign(this.data.analysis, features);
    }

    async loadAnalysisInfo(analysisId) {
        const response = await fetch(`/api/analysis/${analysisId}/info`);
        if (!response.ok) throw new Error('无法加载分析信息');

        this.analysisInfo = await response.json();
    }

    async downloadExcel() {
        try {
            const analysisId = this.getAnalysisId();
            console.log('分析ID:', analysisId);

            if (!analysisId) {
                alert('无法获取分析ID');
                return;
            }

            const response = await fetch(`/api/analysis/${analysisId}/excel`);
            console.log('Excel API响应状态:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Excel API错误:', errorText);
                alert('无法获取Excel文件信息: ' + response.status);
                return;
            }

            const data = await response.json();
            console.log('Excel文件信息:', data);

            if (!data.excel_filename) {
                alert('Excel文件名为空');
                return;
            }

            const downloadUrl = '/download-excel/' + data.excel_filename;
            console.log('下载URL:', downloadUrl);
            window.open(downloadUrl, '_blank');
        } catch (error) {
            console.error('下载Excel失败:', error);
            alert('下载失败: ' + error.message);
        }
    }

    saveAsHtml() {
        try {
            // 创建完整的HTML文档
            const htmlContent = this.generateStandaloneHtml();

            // 创建下载链接
            const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `map_visualization_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('HTML文件已保存！');
        } catch (error) {
            console.error('保存HTML失败:', error);
            alert('保存失败: ' + error.message);
        }
    }

    generateStandaloneHtml() {
        const mapName = this.analysisInfo ?
            (this.currentMode === 'old' ? this.analysisInfo.old_map_filename : this.analysisInfo.new_map_filename) :
            '地图';

        // 获取当前的CSS样式
        const cssContent = this.getVisualizeCss();

        // 获取完整的JavaScript代码
        const jsContent = this.getStandaloneJsCode();

        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图差异可视化 - ${mapName}</title>
    <style>
        ${cssContent}
    </style>
</head>
<body>
    <div class="header">
        <h1>地图差异可视化 - ${mapName}</h1>
        <div class="controls">
            <button class="btn" id="toggleMapBtn">切换到新地图</button>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="stats" id="mapStats">
                <h4>地图统计</h4>
                <div class="stat-item">
                    <span>当前基准:</span>
                    <span id="currentBase">加载中...</span>
                </div>
                <div class="stat-item">
                    <span>节点总数:</span>
                    <span id="nodeCount">0</span>
                </div>
                <div class="stat-item">
                    <span>边总数:</span>
                    <span id="edgeCount">0</span>
                </div>
                <div class="stat-item">
                    <span>孤岛数量:</span>
                    <span id="islandCount">0</span>
                </div>
                <div class="stat-item">
                    <span>跳点边数:</span>
                    <span id="jumpingEdgeCount">0</span>
                </div>
            </div>

            <div class="stats" id="differenceStats" style="display: none;">
                <h4>差异统计</h4>
                <div class="stat-item">
                    <span>新增节点:</span>
                    <span id="addedNodesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>删除节点:</span>
                    <span id="deletedNodesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>修改节点:</span>
                    <span id="modifiedNodesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>新增边:</span>
                    <span id="addedEdgesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>删除边:</span>
                    <span id="deletedEdgesCount">0</span>
                </div>
            </div>

            <div class="legend" id="oldMapLegend">
                <h3>旧地图图例</h3>
            </div>

            <div class="legend" id="newMapLegend" style="display: none;">
                <h3>新地图图例</h3>
            </div>
        </div>

        <div class="map-container">
            <canvas id="mapCanvas"></canvas>

            <div class="zoom-controls">
                <button class="zoom-btn" id="zoomInBtn">+</button>
                <button class="zoom-btn" id="zoomOutBtn">−</button>
                <button class="zoom-btn" id="resetViewBtn" title="重置视图">⌂</button>
            </div>

            <div class="minimap">
                <canvas id="minimapCanvas"></canvas>
                <div class="viewport-indicator" id="viewportIndicator"></div>
            </div>
        </div>
    </div>

    <script>
        // 嵌入静态数据
        window.STATIC_MAP_DATA = ${JSON.stringify(this.data)};
        window.STATIC_ANALYSIS_INFO = ${JSON.stringify(this.analysisInfo)};

        ${jsContent}
    </script>
</body>
</html>`;
    }

    generateStatsHtml() {
        if (!this.analysisInfo) return '';

        const stats = this.analysisInfo.statistics;
        const currentStats = this.currentMode === 'old' ? stats.old_map : stats.new_map;
        const mapName = this.currentMode === 'old' ? this.analysisInfo.old_map_filename : this.analysisInfo.new_map_filename;

        let html = `
            <div class="stats">
                <h4>地图统计</h4>
                <div class="stat-item"><span>当前基准:</span><span>${mapName}</span></div>
                <div class="stat-item"><span>节点总数:</span><span>${currentStats.nodes_count}</span></div>
                <div class="stat-item"><span>边总数:</span><span>${currentStats.edges_count}</span></div>
                <div class="stat-item"><span>孤岛数量:</span><span>${currentStats.islands_count}</span></div>
                <div class="stat-item"><span>跳点边数:</span><span>${currentStats.jumping_edges_count}</span></div>
            </div>
        `;

        if (this.currentMode === 'old') {
            const diffStats = stats.differences;
            html += `
                <div class="stats">
                    <h4>差异统计</h4>
                    <div class="stat-item"><span>新增节点:</span><span>${diffStats.added_nodes_count}</span></div>
                    <div class="stat-item"><span>删除节点:</span><span>${diffStats.deleted_nodes_count}</span></div>
                    <div class="stat-item"><span>修改节点:</span><span>${diffStats.modified_nodes_count}</span></div>
                    <div class="stat-item"><span>新增边:</span><span>${diffStats.added_edges_count}</span></div>
                    <div class="stat-item"><span>删除边:</span><span>${diffStats.deleted_edges_count}</span></div>
                </div>
            `;
        }

        return html;
    }

    generateLegendHtml() {
        if (!this.analysisInfo) return '';

        const stats = this.analysisInfo.statistics;
        const legendItems = this.currentMode === 'old' ? [
            { label: `基础节点 (${stats.old_map.nodes_count})`, color: this.colors.baseNode },
            { label: `基础边 (${stats.old_map.edges_count})`, color: this.colors.baseEdge },
            { label: `新增节点 (${stats.differences.added_nodes_count})`, color: this.colors.addedNode },
            { label: `修改节点 (${stats.differences.modified_nodes_count})`, color: this.colors.modifiedNode },
            { label: `删除节点 (${stats.differences.deleted_nodes_count})`, color: this.colors.deletedNode },
            { label: `孤岛 (${stats.old_map.islands_count})`, color: this.colors.island },
            { label: `跳点边 (${stats.old_map.jumping_edges_count})`, color: this.colors.jumpingEdge }
        ] : [
            { label: `基础节点 (${stats.new_map.nodes_count})`, color: this.colors.baseNode },
            { label: `基础边 (${stats.new_map.edges_count})`, color: this.colors.baseEdge },
            { label: `孤岛 (${stats.new_map.islands_count})`, color: this.colors.island },
            { label: `跳点边 (${stats.new_map.jumping_edges_count})`, color: this.colors.jumpingEdge }
        ];

        let html = `
            <div class="legend">
                <h4>${this.currentMode === 'old' ? '旧地图' : '新地图'}图例</h4>
        `;

        legendItems.forEach(item => {
            html += `
                <div class="legend-item">
                    <div class="legend-color" style="background-color: ${item.color};"></div>
                    <span class="legend-label">${item.label}</span>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    getVisualizeCss() {
        return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f0f0f0;
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
            position: relative;
        }

        .header h1 {
            font-size: 20px;
        }

        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.active {
            background: #e74c3c;
        }

        .btn.active:hover {
            background: #c0392b;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            padding: 20px;
        }

        .map-container {
            flex: 1;
            position: relative;
            background: #fff;
        }

        #mapCanvas {
            display: block;
            cursor: grab;
        }

        #mapCanvas:active {
            cursor: grabbing;
        }

        .legend {
            margin-bottom: 25px;
        }

        .legend h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            font-size: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
            border: 1px solid transparent;
        }

        .legend-item:hover {
            background-color: #f8f9fa;
            border-color: #e9ecef;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 12px;
            border: 1px solid #ccc;
            flex-shrink: 0;
        }

        .legend-label {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        .legend-toggle {
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #3498db;
        }

        .zoom-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 100;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.2s;
        }

        .zoom-btn:hover {
            background: #f0f0f0;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .zoom-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .minimap {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            background: white;
            border: 2px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 100;
            overflow: hidden;
        }

        #minimapCanvas {
            width: 100%;
            height: 100%;
            cursor: pointer;
            display: block;
        }

        .viewport-indicator {
            position: absolute;
            border: 2px solid #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            pointer-events: none;
            border-radius: 2px;
        }

        .stats {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #bdc3c7;
        }

        .stats h4 {
            margin-bottom: 12px;
            color: #2c3e50;
            font-size: 16px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .stat-item span:first-child {
            color: #555;
        }

        .stat-item span:last-child {
            font-weight: bold;
            color: #2c3e50;
        }
        `;
    }

    getStandaloneJsCode() {
        return `
        // 独立版本的地图可视化器
        class StandaloneMapVisualizer {
            constructor() {
                // 地图坐标范围
                this.MAP_BOUNDS = {
                    minX: 113800,
                    maxX: 687950,
                    minY: 97600,
                    maxY: 497950
                };

                // 计算地图尺寸
                this.MAP_WIDTH = this.MAP_BOUNDS.maxX - this.MAP_BOUNDS.minX;
                this.MAP_HEIGHT = this.MAP_BOUNDS.maxY - this.MAP_BOUNDS.minY;

                // 画布和上下文
                this.canvas = document.getElementById('mapCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.minimapCanvas = document.getElementById('minimapCanvas');
                this.minimapCtx = this.minimapCanvas.getContext('2d');

                // 视图状态
                this.scale = 1;
                this.offsetX = 0;
                this.offsetY = 0;
                this.isDragging = false;
                this.lastMouseX = 0;
                this.lastMouseY = 0;

                // 当前显示模式
                this.currentMode = 'old';

                // 图层可见性
                this.layerVisibility = {
                    old: {
                        islands: true,
                        jumpingEdges: true,
                        addedNodes: true,
                        modifiedNodes: true,
                        deletedNodes: true,
                        baseNodes: true,
                        baseEdges: true
                    },
                    new: {
                        islands: true,
                        jumpingEdges: true,
                        baseNodes: true,
                        baseEdges: true
                    }
                };

                // 颜色配置
                this.colors = {
                    baseNode: '#4a90e2',
                    baseEdge: '#7ed321',
                    addedNode: '#50e3c2',
                    modifiedNode: '#f5a623',
                    deletedNode: '#d0021b',
                    island: '#9013fe',
                    jumpingEdge: '#bd10e0'
                };

                // 使用静态数据
                this.data = window.STATIC_MAP_DATA;
                this.analysisInfo = window.STATIC_ANALYSIS_INFO;

                this.init();
            }

            init() {
                this.setupCanvas();
                this.setupEventListeners();
                this.setupLegend();
                this.resetView();
                this.updateStats();
            }

            // 复制所有必要的方法...
            ${this.getEssentialMethods()}
        }

        // 初始化独立可视化器
        window.addEventListener('load', () => {
            new StandaloneMapVisualizer();
        });
        `;
    }

    getEssentialMethods() {
        // 返回所有必要的方法代码（除了API相关的方法）
        return `
            setupCanvas() {
                const container = this.canvas.parentElement;
                this.canvas.width = container.clientWidth;
                this.canvas.height = container.clientHeight;

                this.minimapCanvas.width = 200;
                this.minimapCanvas.height = 150;

                window.addEventListener('resize', () => {
                    this.canvas.width = container.clientWidth;
                    this.canvas.height = container.clientHeight;
                    this.render();
                });
            }

            setupEventListeners() {
                // 地图拖拽
                this.canvas.addEventListener('mousedown', (e) => {
                    this.isDragging = true;
                    this.lastMouseX = e.clientX;
                    this.lastMouseY = e.clientY;
                });

                this.canvas.addEventListener('mousemove', (e) => {
                    if (this.isDragging) {
                        const deltaX = e.clientX - this.lastMouseX;
                        const deltaY = e.clientY - this.lastMouseY;
                        this.offsetX += deltaX;
                        this.offsetY += deltaY;
                        this.lastMouseX = e.clientX;
                        this.lastMouseY = e.clientY;
                        this.render();
                    }
                });

                this.canvas.addEventListener('mouseup', () => {
                    this.isDragging = false;
                });

                this.canvas.addEventListener('mouseleave', () => {
                    this.isDragging = false;
                });

                // 鼠标滚轮缩放
                this.canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const rect = this.canvas.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    this.zoomAt(mouseX, mouseY, zoomFactor);
                });

                // 缩放按钮
                document.getElementById('zoomInBtn').addEventListener('click', () => {
                    this.zoomAt(this.canvas.width / 2, this.canvas.height / 2, 1.2);
                });

                document.getElementById('zoomOutBtn').addEventListener('click', () => {
                    this.zoomAt(this.canvas.width / 2, this.canvas.height / 2, 0.8);
                });

                document.getElementById('resetViewBtn').addEventListener('click', () => {
                    this.resetView();
                });

                // 切换地图模式
                document.getElementById('toggleMapBtn').addEventListener('click', () => {
                    this.toggleMapMode();
                });

                // 小地图点击
                this.minimapCanvas.addEventListener('click', (e) => {
                    const rect = this.minimapCanvas.getBoundingClientRect();
                    const x = (e.clientX - rect.left) / rect.width;
                    const y = (e.clientY - rect.top) / rect.height;
                    this.jumpToMinimapPosition(x, y);
                });
            }

            zoomAt(mouseX, mouseY, zoomFactor) {
                const newScale = Math.max(0.1, Math.min(10, this.scale * zoomFactor));

                if (newScale !== this.scale) {
                    const worldX = (mouseX - this.offsetX) / this.scale;
                    const worldY = (mouseY - this.offsetY) / this.scale;

                    this.scale = newScale;

                    this.offsetX = mouseX - worldX * this.scale;
                    this.offsetY = mouseY - worldY * this.scale;

                    this.render();
                }
            }

            resetView() {
                const padding = 50;
                const scaleX = (this.canvas.width - padding * 2) / this.MAP_WIDTH;
                const scaleY = (this.canvas.height - padding * 2) / this.MAP_HEIGHT;
                this.scale = Math.min(scaleX, scaleY);

                this.offsetX = (this.canvas.width - this.MAP_WIDTH * this.scale) / 2;
                this.offsetY = (this.canvas.height - this.MAP_HEIGHT * this.scale) / 2;

                this.render();
            }

            toggleMapMode() {
                this.currentMode = this.currentMode === 'old' ? 'new' : 'old';

                const btn = document.getElementById('toggleMapBtn');
                btn.textContent = this.currentMode === 'old' ? '切换到新地图' : '切换到旧地图';
                btn.className = this.currentMode === 'old' ? 'btn' : 'btn active';

                document.getElementById('oldMapLegend').style.display = this.currentMode === 'old' ? 'block' : 'none';
                document.getElementById('newMapLegend').style.display = this.currentMode === 'new' ? 'block' : 'none';

                this.updateStats();
                this.render();
            }

            worldToScreen(worldX, worldY) {
                const screenX = (worldX - this.MAP_BOUNDS.minX) * this.scale + this.offsetX;
                const screenY = (worldY - this.MAP_BOUNDS.minY) * this.scale + this.offsetY;
                return { x: screenX, y: screenY };
            }

            render() {
                if (!this.data) return;

                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.drawGrid();

                if (this.currentMode === 'old') {
                    this.renderOldMapMode();
                } else {
                    this.renderNewMapMode();
                }

                this.renderMinimap();
                this.updateDebugInfo();
            }

            drawGrid() {
                this.ctx.strokeStyle = '#f0f0f0';
                this.ctx.lineWidth = 1;

                const gridSize = 10000;
                const startX = Math.floor(this.MAP_BOUNDS.minX / gridSize) * gridSize;
                const startY = Math.floor(this.MAP_BOUNDS.minY / gridSize) * gridSize;

                for (let x = startX; x <= this.MAP_BOUNDS.maxX; x += gridSize) {
                    const screenPos = this.worldToScreen(x, this.MAP_BOUNDS.minY);
                    if (screenPos.x >= -10 && screenPos.x <= this.canvas.width + 10) {
                        this.ctx.beginPath();
                        this.ctx.moveTo(screenPos.x, 0);
                        this.ctx.lineTo(screenPos.x, this.canvas.height);
                        this.ctx.stroke();
                    }
                }

                for (let y = startY; y <= this.MAP_BOUNDS.maxY; y += gridSize) {
                    const screenPos = this.worldToScreen(this.MAP_BOUNDS.minX, y);
                    if (screenPos.y >= -10 && screenPos.y <= this.canvas.height + 10) {
                        this.ctx.beginPath();
                        this.ctx.moveTo(0, screenPos.y);
                        this.ctx.lineTo(this.canvas.width, screenPos.y);
                        this.ctx.stroke();
                    }
                }
            }

            renderOldMapMode() {
                const visibility = this.layerVisibility.old;

                if (visibility.baseEdges && this.data.base_map_edges) {
                    this.drawEdges(this.data.base_map_edges, this.colors.baseEdge, 1);
                }

                if (visibility.jumpingEdges && this.data.analysis.old_map_jumping_edges) {
                    this.data.analysis.old_map_jumping_edges.forEach(item => {
                        this.drawEdge(item.jumping_edge, this.colors.jumpingEdge, 3);
                    });
                }

                if (visibility.baseNodes && this.data.base_map_nodes) {
                    this.drawNodes(this.data.base_map_nodes, this.colors.baseNode, 4);
                }

                if (visibility.addedNodes && this.data.analysis.added_nodes) {
                    this.drawNodes(this.data.analysis.added_nodes, this.colors.addedNode, 6);
                }

                if (visibility.modifiedNodes) {
                    const modifiedNodes = [
                        ...(this.data.analysis.conflicts_id_only || []).map(c => c.new_node),
                        ...(this.data.analysis.conflicts_id_and_attr || []).map(c => c.new_node)
                    ];
                    if (modifiedNodes.length > 0) {
                        this.drawNodes(modifiedNodes, this.colors.modifiedNode, 6);
                    }
                }

                if (visibility.deletedNodes && this.data.analysis.deleted_nodes) {
                    this.drawNodes(this.data.analysis.deleted_nodes, this.colors.deletedNode, 6);
                }

                if (visibility.islands && this.data.analysis.old_map_islands) {
                    this.drawIslands(this.data.analysis.old_map_islands);
                }
            }

            renderNewMapMode() {
                const visibility = this.layerVisibility.new;

                if (visibility.baseEdges) {
                    this.drawEdges(this.data.new_map_edges, this.colors.baseEdge, 1);
                }

                if (visibility.jumpingEdges && this.data.analysis.new_map_jumping_edges) {
                    this.data.analysis.new_map_jumping_edges.forEach(item => {
                        this.drawEdge(item.jumping_edge, this.colors.jumpingEdge, 3);
                    });
                }

                if (visibility.baseNodes) {
                    this.drawNodes(this.data.new_map_nodes, this.colors.baseNode, 4);
                }

                if (visibility.islands && this.data.analysis.new_map_islands) {
                    this.drawIslands(this.data.analysis.new_map_islands);
                }
            }

            drawNodes(nodes, color, size) {
                if (!nodes || nodes.length === 0) return;

                this.ctx.fillStyle = color;
                this.ctx.strokeStyle = '#333';
                this.ctx.lineWidth = 1;
                this.ctx.beginPath();

                nodes.forEach(node => {
                    if (node.x && node.y) {
                        const x = parseFloat(node.x);
                        const y = parseFloat(node.y);

                        if (!isNaN(x) && !isNaN(y)) {
                            const pos = this.worldToScreen(x, y);
                            if (this.isPointVisible(pos.x, pos.y)) {
                                this.ctx.moveTo(pos.x + size, pos.y);
                                this.ctx.arc(pos.x, pos.y, size, 0, 2 * Math.PI);
                            }
                        }
                    }
                });

                this.ctx.fill();
                this.ctx.stroke();
            }

            drawEdges(edges, color, width) {
                if (!edges || edges.length === 0) return;

                this.ctx.strokeStyle = color;
                this.ctx.lineWidth = width;
                this.ctx.beginPath();

                edges.forEach(edge => {
                    const startNode = this.getNodeById(edge.start_node_id);
                    const endNode = this.getNodeById(edge.end_node_id);

                    if (startNode && endNode && startNode.x && startNode.y && endNode.x && endNode.y) {
                        const startX = parseFloat(startNode.x);
                        const startY = parseFloat(startNode.y);
                        const endX = parseFloat(endNode.x);
                        const endY = parseFloat(endNode.y);

                        if (!isNaN(startX) && !isNaN(startY) && !isNaN(endX) && !isNaN(endY)) {
                            const startPos = this.worldToScreen(startX, startY);
                            const endPos = this.worldToScreen(endX, endY);

                            if (this.isLineVisible(startPos.x, startPos.y, endPos.x, endPos.y)) {
                                this.ctx.moveTo(startPos.x, startPos.y);
                                this.ctx.lineTo(endPos.x, endPos.y);
                            }
                        }
                    }
                });

                this.ctx.stroke();
            }

            drawEdge(edge, color, width) {
                const startNode = this.getNodeById(edge.start_node_id);
                const endNode = this.getNodeById(edge.end_node_id);

                if (startNode && endNode && startNode.x && startNode.y && endNode.x && endNode.y) {
                    const startX = parseFloat(startNode.x);
                    const startY = parseFloat(startNode.y);
                    const endX = parseFloat(endNode.x);
                    const endY = parseFloat(endNode.y);

                    if (!isNaN(startX) && !isNaN(startY) && !isNaN(endX) && !isNaN(endY)) {
                        const startPos = this.worldToScreen(startX, startY);
                        const endPos = this.worldToScreen(endX, endY);

                        if (this.isLineVisible(startPos.x, startPos.y, endPos.x, endPos.y)) {
                            this.ctx.strokeStyle = color;
                            this.ctx.lineWidth = width;
                            this.ctx.beginPath();
                            this.ctx.moveTo(startPos.x, startPos.y);
                            this.ctx.lineTo(endPos.x, endPos.y);
                            this.ctx.stroke();
                        }
                    }
                }
            }

            drawIslands(islands) {
                islands.forEach(island => {
                    if (island.nodes && island.nodes.length > 1) {
                        this.ctx.strokeStyle = this.colors.island;
                        this.ctx.lineWidth = 2;
                        this.ctx.setLineDash([5, 5]);

                        const positions = island.nodes
                            .filter(node => node.x && node.y)
                            .map(node => this.worldToScreen(parseFloat(node.x), parseFloat(node.y)));

                        if (positions.length > 1) {
                            const minX = Math.min(...positions.map(p => p.x)) - 10;
                            const maxX = Math.max(...positions.map(p => p.x)) + 10;
                            const minY = Math.min(...positions.map(p => p.y)) - 10;
                            const maxY = Math.max(...positions.map(p => p.y)) + 10;

                            this.ctx.beginPath();
                            this.ctx.rect(minX, minY, maxX - minX, maxY - minY);
                            this.ctx.stroke();
                            this.ctx.setLineDash([]);
                        }
                    }
                });
            }

            getNodeById(nodeId) {
                let nodes = this.currentMode === 'old' ? this.data.base_map_nodes : this.data.new_map_nodes;
                let node = nodes.find(n => n.id === nodeId);

                if (!node) {
                    nodes = this.currentMode === 'old' ? this.data.new_map_nodes : this.data.base_map_nodes;
                    node = nodes.find(n => n.id === nodeId);
                }

                return node;
            }

            isPointVisible(x, y) {
                return x >= -50 && x <= this.canvas.width + 50 &&
                       y >= -50 && y <= this.canvas.height + 50;
            }

            isLineVisible(x1, y1, x2, y2) {
                return this.isPointVisible(x1, y1) || this.isPointVisible(x2, y2) ||
                       (x1 < 0 && x2 > this.canvas.width) || (y1 < 0 && y2 > this.canvas.height);
            }

            setupLegend() {
                this.setupOldMapLegend();
                this.setupNewMapLegend();
            }

            setupOldMapLegend() {
                const container = document.getElementById('oldMapLegend');
                container.innerHTML = '';

                if (!this.data || !this.analysisInfo) return;

                const stats = this.analysisInfo.statistics;
                const legendItems = [
                    { key: 'baseNodes', label: '基础节点 (' + stats.old_map.nodes_count + ')', color: this.colors.baseNode },
                    { key: 'baseEdges', label: '基础边 (' + stats.old_map.edges_count + ')', color: this.colors.baseEdge },
                    { key: 'addedNodes', label: '新增节点 (' + stats.differences.added_nodes_count + ')', color: this.colors.addedNode },
                    { key: 'modifiedNodes', label: '修改节点 (' + stats.differences.modified_nodes_count + ')', color: this.colors.modifiedNode },
                    { key: 'deletedNodes', label: '删除节点 (' + stats.differences.deleted_nodes_count + ')', color: this.colors.deletedNode },
                    { key: 'islands', label: '孤岛 (' + stats.old_map.islands_count + ')', color: this.colors.island },
                    { key: 'jumpingEdges', label: '跳点边 (' + stats.old_map.jumping_edges_count + ')', color: this.colors.jumpingEdge }
                ];

                legendItems.forEach(item => {
                    const div = this.createLegendItem(item, 'old');
                    container.appendChild(div);
                });
            }

            setupNewMapLegend() {
                const container = document.getElementById('newMapLegend');
                container.innerHTML = '';

                if (!this.data || !this.analysisInfo) return;

                const stats = this.analysisInfo.statistics;
                const legendItems = [
                    { key: 'baseNodes', label: '基础节点 (' + stats.new_map.nodes_count + ')', color: this.colors.baseNode },
                    { key: 'baseEdges', label: '基础边 (' + stats.new_map.edges_count + ')', color: this.colors.baseEdge },
                    { key: 'islands', label: '孤岛 (' + stats.new_map.islands_count + ')', color: this.colors.island },
                    { key: 'jumpingEdges', label: '跳点边 (' + stats.new_map.jumping_edges_count + ')', color: this.colors.jumpingEdge }
                ];

                legendItems.forEach(item => {
                    const div = this.createLegendItem(item, 'new');
                    container.appendChild(div);
                });
            }

            createLegendItem(item, mode) {
                const div = document.createElement('div');
                div.className = 'legend-item';

                const colorBox = document.createElement('div');
                colorBox.className = 'legend-color';
                colorBox.style.backgroundColor = item.color;

                const label = document.createElement('span');
                label.className = 'legend-label';
                label.textContent = item.label;

                const toggle = document.createElement('input');
                toggle.type = 'checkbox';
                toggle.className = 'legend-toggle';
                toggle.checked = this.layerVisibility[mode][item.key];
                toggle.addEventListener('change', () => {
                    this.layerVisibility[mode][item.key] = toggle.checked;
                    this.render();
                });

                div.appendChild(colorBox);
                div.appendChild(label);
                div.appendChild(toggle);

                return div;
            }

            renderMinimap() {
                if (!this.data) return;

                this.minimapCtx.clearRect(0, 0, this.minimapCanvas.width, this.minimapCanvas.height);
                this.minimapCtx.strokeStyle = '#ccc';
                this.minimapCtx.lineWidth = 1;
                this.minimapCtx.strokeRect(0, 0, this.minimapCanvas.width, this.minimapCanvas.height);

                this.drawSimplifiedMapOutline();
                this.updateViewportIndicator();
            }

            drawSimplifiedMapOutline() {
                const edges = this.currentMode === 'old' ? this.data.base_map_edges : this.data.new_map_edges;
                const nodes = this.currentMode === 'old' ? this.data.base_map_nodes : this.data.new_map_nodes;

                if (!edges || !nodes || edges.length === 0 || nodes.length === 0) return;

                const nodeMap = {};
                nodes.forEach(node => {
                    if (node.x && node.y) {
                        nodeMap[node.id] = {
                            x: parseFloat(node.x),
                            y: parseFloat(node.y)
                        };
                    }
                });

                const simplifiedEdges = edges.filter((_, index) => index % 10 === 0).slice(0, 20);

                this.minimapCtx.strokeStyle = '#999';
                this.minimapCtx.lineWidth = 1;
                this.minimapCtx.beginPath();

                simplifiedEdges.forEach(edge => {
                    const startNode = nodeMap[edge.start_node_id];
                    const endNode = nodeMap[edge.end_node_id];

                    if (startNode && endNode) {
                        const startX = ((startNode.x - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width;
                        const startY = ((startNode.y - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height;
                        const endX = ((endNode.x - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width;
                        const endY = ((endNode.y - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height;

                        if (!isNaN(startX) && !isNaN(startY) && !isNaN(endX) && !isNaN(endY)) {
                            this.minimapCtx.moveTo(startX, startY);
                            this.minimapCtx.lineTo(endX, endY);
                        }
                    }
                });

                this.minimapCtx.stroke();
            }

            updateViewportIndicator() {
                const indicator = document.getElementById('viewportIndicator');

                const viewLeft = (0 - this.offsetX) / this.scale + this.MAP_BOUNDS.minX;
                const viewTop = (0 - this.offsetY) / this.scale + this.MAP_BOUNDS.minY;
                const viewRight = (this.canvas.width - this.offsetX) / this.scale + this.MAP_BOUNDS.minX;
                const viewBottom = (this.canvas.height - this.offsetY) / this.scale + this.MAP_BOUNDS.minY;

                const left = Math.max(0, ((viewLeft - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width);
                const top = Math.max(0, ((viewTop - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height);
                const right = Math.min(this.minimapCanvas.width, ((viewRight - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width);
                const bottom = Math.min(this.minimapCanvas.height, ((viewBottom - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height);

                indicator.style.left = left + 'px';
                indicator.style.top = top + 'px';
                indicator.style.width = (right - left) + 'px';
                indicator.style.height = (bottom - top) + 'px';
            }

            jumpToMinimapPosition(x, y) {
                const worldX = x * this.MAP_WIDTH + this.MAP_BOUNDS.minX;
                const worldY = y * this.MAP_HEIGHT + this.MAP_BOUNDS.minY;

                this.offsetX = this.canvas.width / 2 - (worldX - this.MAP_BOUNDS.minX) * this.scale;
                this.offsetY = this.canvas.height / 2 - (worldY - this.MAP_BOUNDS.minY) * this.scale;

                this.render();
            }

            updateStats() {
                if (!this.data || !this.analysisInfo) return;

                const currentBaseElement = document.getElementById('currentBase');
                const nodeCountElement = document.getElementById('nodeCount');
                const edgeCountElement = document.getElementById('edgeCount');
                const islandCountElement = document.getElementById('islandCount');
                const jumpingEdgeCountElement = document.getElementById('jumpingEdgeCount');
                const differenceStatsElement = document.getElementById('differenceStats');

                if (this.currentMode === 'old') {
                    const oldStats = this.analysisInfo.statistics.old_map;
                    const diffStats = this.analysisInfo.statistics.differences;

                    if (currentBaseElement) currentBaseElement.textContent = this.analysisInfo.old_map_filename;
                    if (nodeCountElement) nodeCountElement.textContent = oldStats.nodes_count;
                    if (edgeCountElement) edgeCountElement.textContent = oldStats.edges_count;
                    if (islandCountElement) islandCountElement.textContent = oldStats.islands_count;
                    if (jumpingEdgeCountElement) jumpingEdgeCountElement.textContent = oldStats.jumping_edges_count;

                    if (differenceStatsElement) {
                        differenceStatsElement.style.display = 'block';
                        document.getElementById('addedNodesCount').textContent = diffStats.added_nodes_count;
                        document.getElementById('deletedNodesCount').textContent = diffStats.deleted_nodes_count;
                        document.getElementById('modifiedNodesCount').textContent = diffStats.modified_nodes_count;
                        document.getElementById('addedEdgesCount').textContent = diffStats.added_edges_count;
                        document.getElementById('deletedEdgesCount').textContent = diffStats.deleted_edges_count;
                    }
                } else {
                    const newStats = this.analysisInfo.statistics.new_map;

                    if (currentBaseElement) currentBaseElement.textContent = this.analysisInfo.new_map_filename;
                    if (nodeCountElement) nodeCountElement.textContent = newStats.nodes_count;
                    if (edgeCountElement) edgeCountElement.textContent = newStats.edges_count;
                    if (islandCountElement) islandCountElement.textContent = newStats.islands_count;
                    if (jumpingEdgeCountElement) jumpingEdgeCountElement.textContent = newStats.jumping_edges_count;

                    if (differenceStatsElement) {
                        differenceStatsElement.style.display = 'none';
                    }
                }
            }

            updateDebugInfo() {
                // 空实现，独立版本不需要调试信息
            }
        `;
    }

    getAnalysisId() {
        // 优先使用全局变量
        if (window.ANALYSIS_ID) {
            return window.ANALYSIS_ID;
        }

        // 从URL路径获取分析ID
        const pathParts = window.location.pathname.split('/');
        if (pathParts.length >= 3 && pathParts[1] === 'visualize') {
            return pathParts[2];
        }
        return null;
    }
    
    setupCanvas() {
        const container = this.canvas.parentElement;
        this.canvas.width = container.clientWidth;
        this.canvas.height = container.clientHeight;
        
        this.minimapCanvas.width = 200;
        this.minimapCanvas.height = 150;
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.canvas.width = container.clientWidth;
            this.canvas.height = container.clientHeight;
            this.requestRender();
        });
    }
    
    setupEventListeners() {
        // 地图拖拽
        this.canvas.addEventListener('mousedown', (e) => {
            this.isDragging = true;
            this.lastMouseX = e.clientX;
            this.lastMouseY = e.clientY;
        });
        
        this.canvas.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                const deltaX = e.clientX - this.lastMouseX;
                const deltaY = e.clientY - this.lastMouseY;
                this.offsetX += deltaX;
                this.offsetY += deltaY;
                this.lastMouseX = e.clientX;
                this.lastMouseY = e.clientY;
                this.requestRender();
            }
        });
        
        this.canvas.addEventListener('mouseup', () => {
            this.isDragging = false;
        });
        
        this.canvas.addEventListener('mouseleave', () => {
            this.isDragging = false;
        });
        
        // 鼠标滚轮缩放
        this.canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const rect = this.canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;
            
            const zoomFactor = e.deltaY > 0 ? 0.95 : 1.05;
            this.zoomAt(mouseX, mouseY, zoomFactor);
        });
        
        // 缩放按钮
        document.getElementById('zoomInBtn').addEventListener('click', () => {
            this.zoomAt(this.canvas.width / 2, this.canvas.height / 2, 1.05);
        });
        
        document.getElementById('zoomOutBtn').addEventListener('click', () => {
            this.zoomAt(this.canvas.width / 2, this.canvas.height / 2, 0.95);
        });
        
        document.getElementById('resetViewBtn').addEventListener('click', () => {
            this.resetView();
        });
        
        // 切换地图模式
        document.getElementById('toggleMapBtn').addEventListener('click', () => {
            this.toggleMapMode();
        });

        // 下载Excel报告
        const downloadBtn = document.getElementById('downloadExcelBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => {
                this.downloadExcel();
            });
        }

        // 保存HTML
        const saveHtmlBtn = document.getElementById('saveHtmlBtn');
        if (saveHtmlBtn) {
            saveHtmlBtn.addEventListener('click', () => {
                this.saveAsHtml();
            });
        }
        
        // 小地图点击
        this.minimapCanvas.addEventListener('click', (e) => {
            const rect = this.minimapCanvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;
            this.jumpToMinimapPosition(x, y);
        });

        // 添加点击事件监听，用于查看元素详情（需按住Ctrl键）
        this.canvas.addEventListener('click', (e) => {
            if (e.ctrlKey) {
                e.preventDefault(); // 防止默认行为
                const rect = this.canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                this.handleElementClick(mouseX, mouseY, e.clientX, e.clientY);
            }
        });
    }
    
    zoomAt(mouseX, mouseY, zoomFactor) {
        const newScale = Math.max(0, Math.min(1.5, this.scale * zoomFactor)); // 调整缩放范围，防止过小或过大
        if (newScale !== this.scale) {
            // 计算缩放中心
            const worldX = (mouseX - this.offsetX) / this.scale;
            const worldY = (mouseY - this.offsetY) / this.scale;
            
            this.scale = newScale;
            console.log(newScale);
            
            // 调整偏移以保持缩放中心不变
            this.offsetX = mouseX - worldX * this.scale;
            this.offsetY = mouseY - worldY * this.scale;
            
            this.requestRender();
        }
    }
    
    resetView() {
        // 计算合适的缩放比例以显示整个地图
        const padding = 50;
        const scaleX = (this.canvas.width - padding * 2) / this.MAP_WIDTH;
        const scaleY = (this.canvas.height - padding * 2) / this.MAP_HEIGHT;
        this.scale = Math.min(scaleX, scaleY);
        
        // 居中显示
        this.offsetX = (this.canvas.width - this.MAP_WIDTH * this.scale) / 2;
        this.offsetY = (this.canvas.height - this.MAP_HEIGHT * this.scale) / 2;
        
        this.requestRender();
    }
    
    toggleMapMode() {
        this.currentMode = this.currentMode === 'old' ? 'new' : 'old';
        
        const btn = document.getElementById('toggleMapBtn');
        btn.textContent = this.currentMode === 'old' ? '切换到新地图' : '切换到旧地图';
        btn.className = this.currentMode === 'old' ? 'btn' : 'btn active';
        
        // 切换图例显示
        document.getElementById('oldMapLegend').style.display = this.currentMode === 'old' ? 'block' : 'none';
        document.getElementById('newMapLegend').style.display = this.currentMode === 'new' ? 'block' : 'none';
        
        this.updateStats();
        this.requestRender();
    }
    
    worldToScreen(worldX, worldY) {
        const screenX = (worldX - this.MAP_BOUNDS.minX) * this.scale + this.offsetX;
        const screenY = (worldY - this.MAP_BOUNDS.minY) * this.scale + this.offsetY;
        return { x: screenX, y: screenY };
    }
    
    screenToWorld(screenX, screenY) {
        const worldX = (screenX - this.offsetX) / this.scale + this.MAP_BOUNDS.minX;
        const worldY = (screenY - this.offsetY) / this.scale + this.MAP_BOUNDS.minY;
        return { x: worldX, y: worldY };
    }
    
    requestRender() {
        if (!this.renderRequested) {
            this.renderRequested = true;
            requestAnimationFrame(() => {
                this.render();
                this.renderRequested = false;
            });
        }
    }
    
    render() {
        if (!this.data) return;

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 计算可见世界范围（带缓冲区）
        const buffer = 50 / this.scale; // 世界坐标缓冲
        const viewMinX = (0 - this.offsetX) / this.scale + this.MAP_BOUNDS.minX - buffer;
        const viewMaxX = (this.canvas.width - this.offsetX) / this.scale + this.MAP_BOUNDS.minX + buffer;
        const viewMinY = (0 - this.offsetY) / this.scale + this.MAP_BOUNDS.minY - buffer;
        const viewMaxY = (this.canvas.height - this.offsetY) / this.scale + this.MAP_BOUNDS.minY + buffer;

        this.viewMinX = viewMinX;
        this.viewMaxX = viewMaxX;
        this.viewMinY = viewMinY;
        this.viewMaxY = viewMaxY;

        // 绘制网格背景
        this.drawGrid();

        if (this.currentMode === 'old') {
            this.renderOldMapMode();
        } else {
            this.renderNewMapMode();
        }
        
        // *** 新增：绘制高亮效果 ***
        this.drawHighlight();

        this.renderMinimap();
        this.updateDebugInfo();
    }

    updateDebugInfo() {
        // 更新调试信息（如果存在）
        const scaleElement = document.getElementById('debugScale');
        const offsetXElement = document.getElementById('debugOffsetX');
        const offsetYElement = document.getElementById('debugOffsetY');

        if (scaleElement) scaleElement.textContent = this.scale.toFixed(3);
        if (offsetXElement) offsetXElement.textContent = Math.round(this.offsetX);
        if (offsetYElement) offsetYElement.textContent = Math.round(this.offsetY);
    }
    
    drawGrid() {
        this.ctx.strokeStyle = '#f0f0f0';
        this.ctx.lineWidth = 1;
        
        const gridSize = 10000; // 网格大小
        const startX = Math.floor(this.MAP_BOUNDS.minX / gridSize) * gridSize;
        const startY = Math.floor(this.MAP_BOUNDS.minY / gridSize) * gridSize;
        
        for (let x = startX; x <= this.MAP_BOUNDS.maxX; x += gridSize) {
            const screenPos = this.worldToScreen(x, this.MAP_BOUNDS.minY);
            if (screenPos.x >= -10 && screenPos.x <= this.canvas.width + 10) {
                this.ctx.beginPath();
                this.ctx.moveTo(screenPos.x, 0);
                this.ctx.lineTo(screenPos.x, this.canvas.height);
                this.ctx.stroke();
            }
        }
        
        for (let y = startY; y <= this.MAP_BOUNDS.maxY; y += gridSize) {
            const screenPos = this.worldToScreen(this.MAP_BOUNDS.minX, y);
            if (screenPos.y >= -10 && screenPos.y <= this.canvas.height + 10) {
                this.ctx.beginPath();
                this.ctx.moveTo(0, screenPos.y);
                this.ctx.lineTo(this.canvas.width, screenPos.y);
                this.ctx.stroke();
            }
        }
    }
    
    showError(message) {
        document.getElementById('loadingOverlay').innerHTML =
            `<div class="error-message">${message}</div>`;
    }

    renderOldMapMode() {
        const visibility = this.layerVisibility.old;

        // 绘制基础边
        if (visibility.baseEdges && this.data.base_map_edges) {
            this.drawEdges(this.data.base_map_edges, this.colors.baseEdge, 1);
        }

        // 绘制跳点边
        if (visibility.jumpingEdges && this.data.analysis.old_map_jumping_edges) {
            this.data.analysis.old_map_jumping_edges.forEach(item => {
                this.drawEdge(item.jumping_edge, this.colors.jumpingEdge, 3);
            });
        }


        const radius = this.scale < 0.01 ? 1 : 4;

        // 绘制基础节点
        if (visibility.baseNodes && this.data.base_map_nodes) {
            // 使用动态计算的半径
            this.drawNodes(this.data.base_map_nodes, this.colors.baseNode, radius);
        }

        // 绘制差异节点
        if (visibility.addedNodes && this.data.analysis.added_nodes) {
            // 使用动态计算的半径
            this.drawNodes(this.data.analysis.added_nodes, this.colors.addedNode, radius);
        }

        if (visibility.modifiedNodes) {
            const modifiedNodes = [
                ...(this.data.analysis.conflicts_id_only || []).map(c => c.new_node),
                ...(this.data.analysis.conflicts_id_and_attr || []).map(c => c.new_node)
            ].filter(node => node);
            if (modifiedNodes.length > 0) {
                // 使用动态计算的半径
                this.drawNodes(modifiedNodes, this.colors.modifiedNode, radius);
            }
        }

        if (visibility.deletedNodes && this.data.analysis.deleted_nodes) {
            // 使用动态计算的半径
            this.drawNodes(this.data.analysis.deleted_nodes, this.colors.deletedNode, radius);
        }

        // 绘制孤岛
        if (visibility.islands && this.data.analysis.old_map_islands) {
            this.drawIslands(this.data.analysis.old_map_islands);
        }
    }

    renderNewMapMode() {
        const visibility = this.layerVisibility.new;

        // 绘制基础边
        if (visibility.baseEdges) {
            this.drawEdges(this.data.new_map_edges, this.colors.baseEdge, 1);
        }

        // 绘制跳点边
        if (visibility.jumpingEdges && this.data.analysis.new_map_jumping_edges) {
            this.data.analysis.new_map_jumping_edges.forEach(item => {
                this.drawEdge(item.jumping_edge, this.colors.jumpingEdge, 3);
            });
        }

        // 绘制基础节点
        if (visibility.baseNodes) {
            this.drawNodes(this.data.new_map_nodes, this.colors.baseNode, 2);
        }

        // 绘制孤岛
        if (visibility.islands && this.data.analysis.new_map_islands) {
            this.drawIslands(this.data.analysis.new_map_islands);
        }
    }

    drawNodes(nodes, color, size) {
        if (!nodes || nodes.length === 0) return;

        this.ctx.fillStyle = color;
        this.ctx.strokeStyle = '#333';
        this.ctx.lineWidth = 1;

        // 批量绘制节点
        this.ctx.beginPath();

        nodes.forEach(node => {
            const x = node.x;
            const y = node.y;

            if (isNaN(x) || isNaN(y)) return;

            if (x < this.viewMinX || x > this.viewMaxX || y < this.viewMinY || y > this.viewMaxY) return;

            const pos = this.worldToScreen(x, y);
            this.ctx.moveTo(pos.x + size, pos.y);
            this.ctx.arc(pos.x, pos.y, size, 0, 2 * Math.PI);
        });

        this.ctx.fill();
        this.ctx.stroke();
    }

    drawEdges(edges, color, width) {
        if (!edges || edges.length === 0) return;

        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = width;
        this.ctx.beginPath();

        edges.forEach(edge => {
            const startNode = this.getNodeById(edge.start_node_id);
            const endNode = this.getNodeById(edge.end_node_id);

            if (!startNode || !endNode) return;

            const startX = startNode.x;
            const startY = startNode.y;
            const endX = endNode.x;
            const endY = endNode.y;

            if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) return;

            // AABB 测试：检查线段包围盒是否与可见区域相交
            if (Math.max(startX, endX) < this.viewMinX || Math.min(startX, endX) > this.viewMaxX ||
                Math.max(startY, endY) < this.viewMinY || Math.min(startY, endY) > this.viewMaxY) return;

            const startPos = this.worldToScreen(startX, startY);
            const endPos = this.worldToScreen(endX, endY);

            this.ctx.moveTo(startPos.x, startPos.y);
            this.ctx.lineTo(endPos.x, endPos.y);
        });

        this.ctx.stroke();
    }

    drawEdge(edge, color, width) {
        const startNode = this.getNodeById(edge.start_node_id);
        const endNode = this.getNodeById(edge.end_node_id);

        if (!startNode || !endNode) return;

        const startX = startNode.x;
        const startY = startNode.y;
        const endX = endNode.x;
        const endY = endNode.y;

        if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) return;

        // AABB 测试
        if (Math.max(startX, endX) < this.viewMinX || Math.min(startX, endX) > this.viewMaxX ||
            Math.max(startY, endY) < this.viewMinY || Math.min(startY, endY) > this.viewMaxY) return;

        const startPos = this.worldToScreen(startX, startY);
        const endPos = this.worldToScreen(endX, endY);

        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = width;
        this.ctx.beginPath();
        this.ctx.moveTo(startPos.x, startPos.y);
        this.ctx.lineTo(endPos.x, endPos.y);
        this.ctx.stroke();
    }

    drawIslands(islands) {
        islands.forEach(island => {
            if (!island.nodes || island.nodes.length <= 1) return;

            const xs = island.nodes.map(node => node.x).filter(x => !isNaN(x));
            const ys = island.nodes.map(node => node.y).filter(y => !isNaN(y));

            if (xs.length < 2) return;

            const minX = Math.min(...xs);
            const maxX = Math.max(...xs);
            const minY = Math.min(...ys);
            const maxY = Math.max(...ys);

            // 检查孤岛包围盒是否与可见区域相交
            if (maxX < this.viewMinX || minX > this.viewMaxX || maxY < this.viewMinY || minY > this.viewMaxY) return;

            // 绘制
            this.ctx.strokeStyle = this.colors.island;
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([5, 5]);

            const positions = island.nodes
                .filter(node => !isNaN(node.x) && !isNaN(node.y))
                .map(node => this.worldToScreen(node.x, node.y));

            const screenMinX = Math.min(...positions.map(p => p.x)) - 10;
            const screenMaxX = Math.max(...positions.map(p => p.x)) + 10;
            const screenMinY = Math.min(...positions.map(p => p.y)) - 10;
            const screenMaxY = Math.max(...positions.map(p => p.y)) + 10;

            this.ctx.beginPath();
            this.ctx.rect(screenMinX, screenMinY, screenMaxX - screenMinX, screenMaxY - screenMinY);
            this.ctx.stroke();
            this.ctx.setLineDash([]);
        });
    }

    // *** 新增：绘制高亮效果 ***
    drawHighlight() {
        if (!this.selectedElement) return;

        const { type, data } = this.selectedElement;
        this.ctx.save(); // 保存当前上下文状态，避免污染其他绘制

        if (type === 'node') {
            const pos = this.worldToScreen(data.x, data.y);
            
            this.ctx.strokeStyle = this.colors.highlight;
            this.ctx.lineWidth = 3;
            this.ctx.fillStyle = 'rgba(255, 255, 0, 0.4)';

            this.ctx.beginPath();
            this.ctx.arc(pos.x, pos.y, 10, 0, 2 * Math.PI); // 绘制一个更大的光圈
            this.ctx.fill();
            this.ctx.stroke();

        } else if (type === 'edge') {
            const startNode = this.getNodeById(data.start_node_id);
            const endNode = this.getNodeById(data.end_node_id);

            if (startNode && endNode) {
                const startPos = this.worldToScreen(startNode.x, startNode.y);
                const endPos = this.worldToScreen(endNode.x, endNode.y);

                this.ctx.strokeStyle = this.colors.highlight;
                this.ctx.lineWidth = 5; // 使用更粗的线条

                this.ctx.beginPath();
                this.ctx.moveTo(startPos.x, startPos.y);
                this.ctx.lineTo(endPos.x, endPos.y);
                this.ctx.stroke();
            }
        }

        this.ctx.restore(); // 恢复上下文状态
    }

    getNodeById(nodeId) {
        const nodeMap = this.currentMode === 'old' ? this.oldNodeMap : this.newNodeMap;
        return nodeMap.get(nodeId);
    }

    setupLegend() {
        console.log('设置图例...');
        this.setupOldMapLegend();
        this.setupNewMapLegend();
        console.log('图例设置完成');
    }

    setupOldMapLegend() {
        const container = document.getElementById('oldMapLegend');
        container.innerHTML = ''; // 清空现有内容以防重复

        if (!this.data || !this.analysisInfo) return;

        const stats = this.analysisInfo.statistics;
        const legendItems = [
            { key: 'baseNodes', label: `基础节点 (${stats.old_map.nodes_count})`, color: this.colors.baseNode },
            { key: 'baseEdges', label: `基础边 (${stats.old_map.edges_count})`, color: this.colors.baseEdge },
            { key: 'addedNodes', label: `新增节点 (${stats.differences.added_nodes_count})`, color: this.colors.addedNode },
            { key: 'modifiedNodes', label: `修改节点 (${stats.differences.modified_nodes_count})`, color: this.colors.modifiedNode },
            { key: 'deletedNodes', label: `删除节点 (${stats.differences.deleted_nodes_count})`, color: this.colors.deletedNode },
            { key: 'islands', label: `孤岛 (${stats.old_map.islands_count})`, color: this.colors.island },
            { key: 'jumpingEdges', label: `跳点边 (${stats.old_map.jumping_edges_count})`, color: this.colors.jumpingEdge }
        ];

        legendItems.forEach(item => {
            const div = this.createLegendItem(item, 'old');
            container.appendChild(div);
        });
    }

    setupNewMapLegend() {
        const container = document.getElementById('newMapLegend');
        container.innerHTML = ''; // 清空现有内容以防重复

        if (!this.data || !this.analysisInfo) return;

        const stats = this.analysisInfo.statistics;
        const legendItems = [
            { key: 'baseNodes', label: `基础节点 (${stats.new_map.nodes_count})`, color: this.colors.baseNode },
            { key: 'baseEdges', label: `基础边 (${stats.new_map.edges_count})`, color: this.colors.baseEdge },
            { key: 'islands', label: `孤岛 (${stats.new_map.islands_count})`, color: this.colors.island },
            { key: 'jumpingEdges', label: `跳点边 (${stats.new_map.jumping_edges_count})`, color: this.colors.jumpingEdge }
        ];

        legendItems.forEach(item => {
            const div = this.createLegendItem(item, 'new');
            container.appendChild(div);
        });
    }

    createLegendItem(item, mode) {
        const div = document.createElement('div');
        div.className = 'legend-item';

        const colorBox = document.createElement('div');
        colorBox.className = 'legend-color';
        colorBox.style.backgroundColor = item.color;

        const label = document.createElement('span');
        label.className = 'legend-label';
        label.textContent = item.label;

        const toggle = document.createElement('input');
        toggle.type = 'checkbox';
        toggle.className = 'legend-toggle';
        toggle.checked = this.layerVisibility[mode][item.key];
        toggle.addEventListener('change', () => {
            this.layerVisibility[mode][item.key] = toggle.checked;
            this.requestRender();
        });

        div.appendChild(colorBox);
        div.appendChild(label);
        div.appendChild(toggle);

        return div;
    }

    renderMinimap() {
        if (!this.data) return;

        this.minimapCtx.clearRect(0, 0, this.minimapCanvas.width, this.minimapCanvas.height);

        // 绘制地图边界
        this.minimapCtx.strokeStyle = '#ccc';
        this.minimapCtx.lineWidth = 1;
        this.minimapCtx.strokeRect(0, 0, this.minimapCanvas.width, this.minimapCanvas.height);

        // 绘制简化的地图轮廓 - 只显示几条主要的边
        this.drawSimplifiedMapOutline();

        // 绘制当前视口
        this.updateViewportIndicator();
    }

    drawSimplifiedMapOutline() {
        const edges = this.currentMode === 'old' ? this.data.base_map_edges : this.data.new_map_edges;
        const nodes = this.currentMode === 'old' ? this.data.base_map_nodes : this.data.new_map_nodes;

        if (!edges || !nodes || edges.length === 0 || nodes.length === 0) return;

        // 创建节点查找表
        const nodeMap = {};
        nodes.forEach(node => {
            if (!isNaN(node.x) && !isNaN(node.y)) {
                nodeMap[node.id] = {
                    x: node.x,
                    y: node.y
                };
            }
        });

        // 只绘制每10条边中的1条，减少渲染负担
        const simplifiedEdges = edges.filter((_, index) => index % 10 === 0).slice(0, 20);

        this.minimapCtx.strokeStyle = '#999';
        this.minimapCtx.lineWidth = 1;
        this.minimapCtx.beginPath();

        simplifiedEdges.forEach(edge => {
            const startNode = nodeMap[edge.start_node_id];
            const endNode = nodeMap[edge.end_node_id];

            if (startNode && endNode) {
                const startX = ((startNode.x - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width;
                const startY = ((startNode.y - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height;
                const endX = ((endNode.x - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width;
                const endY = ((endNode.y - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height;

                if (!isNaN(startX) && !isNaN(startY) && !isNaN(endX) && !isNaN(endY)) {
                    this.minimapCtx.moveTo(startX, startY);
                    this.minimapCtx.lineTo(endX, endY);
                }
            }
        });

        this.minimapCtx.stroke();
    }

    updateViewportIndicator() {
        const indicator = document.getElementById('viewportIndicator');

        // 计算当前视口在世界坐标中的范围
        const viewLeft = (0 - this.offsetX) / this.scale + this.MAP_BOUNDS.minX;
        const viewTop = (0 - this.offsetY) / this.scale + this.MAP_BOUNDS.minY;
        const viewRight = (this.canvas.width - this.offsetX) / this.scale + this.MAP_BOUNDS.minX;
        const viewBottom = (this.canvas.height - this.offsetY) / this.scale + this.MAP_BOUNDS.minY;

        // 转换为小地图坐标
        const left = Math.max(0, ((viewLeft - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width);
        const top = Math.max(0, ((viewTop - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height);
        const right = Math.min(this.minimapCanvas.width, ((viewRight - this.MAP_BOUNDS.minX) / this.MAP_WIDTH) * this.minimapCanvas.width);
        const bottom = Math.min(this.minimapCanvas.height, ((viewBottom - this.MAP_BOUNDS.minY) / this.MAP_HEIGHT) * this.minimapCanvas.height);

        indicator.style.left = left + 'px';
        indicator.style.top = top + 'px';
        indicator.style.width = (right - left) + 'px';
        indicator.style.height = (bottom - top) + 'px';
    }

    jumpToMinimapPosition(x, y) {
        // 将小地图坐标转换为世界坐标
        const worldX = x * this.MAP_WIDTH + this.MAP_BOUNDS.minX;
        const worldY = y * this.MAP_HEIGHT + this.MAP_BOUNDS.minY;

        // 将视图中心移动到该位置
        this.offsetX = this.canvas.width / 2 - (worldX - this.MAP_BOUNDS.minX) * this.scale;
        this.offsetY = this.canvas.height / 2 - (worldY - this.MAP_BOUNDS.minY) * this.scale;

        this.requestRender();
    }

    updateStats() {
        if (!this.data || !this.analysisInfo) return;

        const currentBaseElement = document.getElementById('currentBase');
        const nodeCountElement = document.getElementById('nodeCount');
        const edgeCountElement = document.getElementById('edgeCount');
        const islandCountElement = document.getElementById('islandCount');
        const jumpingEdgeCountElement = document.getElementById('jumpingEdgeCount');
        const differenceStatsElement = document.getElementById('differenceStats');

        if (this.currentMode === 'old') {
            const oldStats = this.analysisInfo.statistics.old_map;
            const diffStats = this.analysisInfo.statistics.differences;

            if (currentBaseElement) currentBaseElement.textContent = this.analysisInfo.old_map_filename;
            if (nodeCountElement) nodeCountElement.textContent = oldStats.nodes_count;
            if (edgeCountElement) edgeCountElement.textContent = oldStats.edges_count;
            if (islandCountElement) islandCountElement.textContent = oldStats.islands_count;
            if (jumpingEdgeCountElement) jumpingEdgeCountElement.textContent = oldStats.jumping_edges_count;

            // 显示差异统计
            if (differenceStatsElement) {
                differenceStatsElement.style.display = 'block';
                document.getElementById('addedNodesCount').textContent = diffStats.added_nodes_count;
                document.getElementById('deletedNodesCount').textContent = diffStats.deleted_nodes_count;
                document.getElementById('modifiedNodesCount').textContent = diffStats.modified_nodes_count;
                document.getElementById('addedEdgesCount').textContent = diffStats.added_edges_count;
                document.getElementById('deletedEdgesCount').textContent = diffStats.deleted_edges_count;
            }
        } else {
            const newStats = this.analysisInfo.statistics.new_map;

            if (currentBaseElement) currentBaseElement.textContent = this.analysisInfo.new_map_filename;
            if (nodeCountElement) nodeCountElement.textContent = newStats.nodes_count;
            if (edgeCountElement) edgeCountElement.textContent = newStats.edges_count;
            if (islandCountElement) islandCountElement.textContent = newStats.islands_count;
            if (jumpingEdgeCountElement) jumpingEdgeCountElement.textContent = newStats.jumping_edges_count;

            // 隐藏差异统计
            if (differenceStatsElement) {
                differenceStatsElement.style.display = 'none';
            }
        }
    }

    // *** 修改：处理元素点击 ***
    handleElementClick(screenX, screenY, clientX, clientY) {
        const worldPos = this.screenToWorld(screenX, screenY);
        const topElement = this.findTopmostElementAtPosition(worldPos.x, worldPos.y);

        this.selectedElement = topElement; // 更新选中的元素

        if (topElement) {
            this.showPopup(topElement, clientX, clientY);
        } else {
            this.hidePopup();
        }

        this.requestRender(); // 请求重绘以显示/清除高亮
    }

    // *** 新增/替换：查找最顶层的单个元素 ***
    findTopmostElementAtPosition(worldX, worldY) {
        const tolerance = 10 / this.scale; // 世界坐标中的容差
        let closestNode = null;
        let minNodeDist = Infinity;

        // 1. 优先查找节点
        const allNodes = this.getAllVisibleNodes();
        allNodes.forEach(node => {
            const dist = Math.hypot(node.x - worldX, node.y - worldY);
            if (dist < tolerance && dist < minNodeDist) {
                minNodeDist = dist;
                closestNode = node;
            }
        });

        // 如果找到了最近的节点，直接返回
        if (closestNode) {
            return { type: 'node', data: closestNode };
        }

        // 2. 如果没有找到节点，再查找边
        let closestEdge = null;
        let minEdgeDist = Infinity;
        const allEdges = this.getAllVisibleEdges();
        const nodeMap = this.currentMode === 'old' ? this.oldNodeMap : this.newNodeMap;

        allEdges.forEach(edge => {
            const startNode = nodeMap.get(edge.start_node_id);
            const endNode = nodeMap.get(edge.end_node_id);
            if (startNode && endNode) {
                const dist = this.distanceToLineSegment(worldX, worldY, startNode.x, startNode.y, endNode.x, endNode.y);
                if (dist < tolerance && dist < minEdgeDist) {
                    minEdgeDist = dist;
                    closestEdge = edge;
                }
            }
        });

        if (closestEdge) {
            return { type: 'edge', data: closestEdge };
        }

        return null; // 什么都没找到
    }

    // *** 新增：获取所有可见节点（根据层可见性）***
    getAllVisibleNodes() {
        const visibility = this.layerVisibility[this.currentMode];
        let nodes = [];

        if (this.currentMode === 'old') {
            if (visibility.baseNodes) nodes = nodes.concat(this.data.base_map_nodes);
            if (visibility.addedNodes) nodes = nodes.concat(this.data.analysis.added_nodes);
            if (visibility.modifiedNodes) {
                const modified = [
                    ...this.data.analysis.conflicts_id_only.map(c => c.new_node),
                    ...this.data.analysis.conflicts_id_and_attr.map(c => c.new_node)
                ].filter(n => n);
                nodes = nodes.concat(modified);
            }
            if (visibility.deletedNodes) nodes = nodes.concat(this.data.analysis.deleted_nodes);
        } else {
            if (visibility.baseNodes) nodes = nodes.concat(this.data.new_map_nodes);
        }

        return nodes.filter(node => node && !isNaN(node.x) && !isNaN(node.y));
    }

    // *** 新增：获取所有可见边 ***
    getAllVisibleEdges() {
        const visibility = this.layerVisibility[this.currentMode];
        let edges = [];

        if (this.currentMode === 'old') {
            if (visibility.baseEdges) edges = edges.concat(this.data.base_map_edges);
            if (visibility.jumpingEdges) edges = edges.concat(this.data.analysis.old_map_jumping_edges.map(item => item.jumping_edge));
        } else {
            if (visibility.baseEdges) edges = edges.concat(this.data.new_map_edges);
            if (visibility.jumpingEdges) edges = edges.concat(this.data.analysis.new_map_jumping_edges.map(item => item.jumping_edge));
        }

        return edges.filter(edge => edge);
    }

    // *** 新增：计算点到线段的最短距离 ***
    distanceToLineSegment(px, py, x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        const lenSq = dx * dx + dy * dy;
        if (lenSq === 0) return Math.hypot(px - x1, py - y1); // 线段是一个点

        let t = ((px - x1) * dx + (py - y1) * dy) / lenSq;
        t = Math.max(0, Math.min(1, t)); // 确保投影点在线段上

        const projX = x1 + t * dx;
        const projY = y1 + t * dy;

        return Math.hypot(px - projX, py - projY);
    }

    // *** 修改：显示单个元素的气泡 ***
    showPopup(element, clientX, clientY) {
        this.hidePopup(); // 先关闭旧的

        this.popup = document.createElement('div');
        this.popup.className = 'element-popup';
        this.popup.style.position = 'absolute';
        this.popup.style.left = `${clientX + 10}px`;
        this.popup.style.top = `${clientY + 10}px`;
        this.popup.style.backgroundColor = 'white';
        this.popup.style.border = '1px solid black';
        this.popup.style.padding = '10px';
        this.popup.style.zIndex = '1000';
        this.popup.style.maxWidth = '300px';
        this.popup.style.overflow = 'auto';

        let content = '<h3>元素详情</h3>';
        const { type, data } = element;
        
        if (type === 'node') {
            content += `<strong>节点</strong><br>`;
            content += `ID: ${data.id}<br>`;
            content += `X: ${data.x.toFixed(2)}<br>`;
            content += `Y: ${data.y.toFixed(2)}<br>`;
            // 可以添加更多节点属性
        } else if (type === 'edge') {
            content += `<strong>边</strong><br>`;
            content += `起始节点ID: ${data.start_node_id}<br>`;
            content += `结束节点ID: ${data.end_node_id}<br>`;
            // 可以添加更多边属性
        }

        this.popup.innerHTML = content;
        document.body.appendChild(this.popup);

        // 添加关闭监听器
        this.popupCloseListener = (e) => {
            // 如果点击的不是弹窗本身，则关闭它
            if (this.popup && !this.popup.contains(e.target)) {
                this.hidePopup();
            }
        };
        setTimeout(() => {
            document.addEventListener('click', this.popupCloseListener);
        }, 0); // 延迟添加以防止立即触发
    }

    // *** 修改：隐藏气泡并清除选择状态 ***
    hidePopup() {
        if (this.popup) {
            this.popup.remove();
            this.popup = null;
        }
        if (this.popupCloseListener) {
            document.removeEventListener('click', this.popupCloseListener);
            this.popupCloseListener = null;
        }
        // 如果之前有选中的元素，清除它并请求重绘以移除高亮
        if (this.selectedElement) {
            this.selectedElement = null;
            this.requestRender();
        }
    }
}

// 初始化可视化器
window.addEventListener('load', () => {
    new MapVisualizer();
});